import { io, Socket } from 'socket.io-client';

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(token: string) {
    if (this.socket?.connected) {
      return;
    }

    const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';

    this.socket = io(socketUrl, {
      auth: {
        token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket?.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.handleReconnect();
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    // Handle authentication errors
    this.socket.on('auth_error', (error) => {
      console.error('Socket auth error:', error);
      this.disconnect();
      // Redirect to login or refresh token
      window.location.href = '/';
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.socket?.connect();
      }, Math.pow(2, this.reconnectAttempts) * 1000); // Exponential backoff
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Location updates
  updateLocation(data: {
    childId: string;
    latitude: number;
    longitude: number;
    parkId?: string;
  }) {
    this.socket?.emit('updateLocation', data);
  }

  onLocationUpdateConfirmed(callback: (data: any) => void) {
    this.socket?.on('locationUpdateConfirmed', callback);
  }

  onChildLocationUpdate(callback: (data: any) => void) {
    this.socket?.on('childLocationUpdate', callback);
  }

  // Park activities
  joinPark(data: { parkId: string; childId: string }) {
    this.socket?.emit('joinPark', data);
  }

  leavePark(data: { parkId: string; childId: string }) {
    this.socket?.emit('leavePark', data);
  }

  onChildJoinedPark(callback: (data: any) => void) {
    this.socket?.on('childJoinedPark', callback);
  }

  onChildLeftPark(callback: (data: any) => void) {
    this.socket?.on('childLeftPark', callback);
  }

  onParkActivityUpdate(callback: (data: any) => void) {
    this.socket?.on('parkActivityUpdate', callback);
  }

  // Play sessions
  startPlaySession(data: {
    childId: string;
    parkId: string;
    gameId?: string;
  }) {
    this.socket?.emit('startPlaySession', data);
  }

  endPlaySession(data: { sessionId: string }) {
    this.socket?.emit('endPlaySession', data);
  }

  onPlaySessionStarted(callback: (data: any) => void) {
    this.socket?.on('playSessionStarted', callback);
  }

  onPlaySessionEnded(callback: (data: any) => void) {
    this.socket?.on('playSessionEnded', callback);
  }

  onPlaySessionStartConfirmed(callback: (data: any) => void) {
    this.socket?.on('playSessionStartConfirmed', callback);
  }

  onPlaySessionEndConfirmed(callback: (data: any) => void) {
    this.socket?.on('playSessionEndConfirmed', callback);
  }

  // Play requests
  onNewPlayRequest(callback: (data: any) => void) {
    this.socket?.on('newPlayRequest', callback);
  }

  onPlayRequestUpdate(callback: (data: any) => void) {
    this.socket?.on('playRequestUpdate', callback);
  }

  // Messaging
  sendMessage(data: {
    recipientId: string;
    message: string;
    type?: string;
  }) {
    this.socket?.emit('sendMessage', data);
  }

  onNewMessage(callback: (data: any) => void) {
    this.socket?.on('newMessage', callback);
  }

  onMessageSent(callback: (data: any) => void) {
    this.socket?.on('messageSent', callback);
  }

  // Badge notifications
  onBadgeEarned(callback: (data: any) => void) {
    this.socket?.on('badgeEarned', callback);
  }

  // Generic event listeners
  on(event: string, callback: (data: any) => void) {
    this.socket?.on(event, callback);
  }

  off(event: string, callback?: (data: any) => void) {
    this.socket?.off(event, callback);
  }

  emit(event: string, data: any) {
    this.socket?.emit(event, data);
  }

  // Utility methods
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  getSocketId(): string | undefined {
    return this.socket?.id;
  }

  // Clean up all listeners
  removeAllListeners() {
    this.socket?.removeAllListeners();
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;

// Hook for React components
export const useSocket = () => {
  return {
    connect: socketService.connect.bind(socketService),
    disconnect: socketService.disconnect.bind(socketService),
    updateLocation: socketService.updateLocation.bind(socketService),
    joinPark: socketService.joinPark.bind(socketService),
    leavePark: socketService.leavePark.bind(socketService),
    startPlaySession: socketService.startPlaySession.bind(socketService),
    endPlaySession: socketService.endPlaySession.bind(socketService),
    sendMessage: socketService.sendMessage.bind(socketService),
    on: socketService.on.bind(socketService),
    off: socketService.off.bind(socketService),
    emit: socketService.emit.bind(socketService),
    isConnected: socketService.isSocketConnected.bind(socketService),
    getSocketId: socketService.getSocketId.bind(socketService),
    
    // Event listeners
    onLocationUpdateConfirmed: socketService.onLocationUpdateConfirmed.bind(socketService),
    onChildLocationUpdate: socketService.onChildLocationUpdate.bind(socketService),
    onChildJoinedPark: socketService.onChildJoinedPark.bind(socketService),
    onChildLeftPark: socketService.onChildLeftPark.bind(socketService),
    onParkActivityUpdate: socketService.onParkActivityUpdate.bind(socketService),
    onPlaySessionStarted: socketService.onPlaySessionStarted.bind(socketService),
    onPlaySessionEnded: socketService.onPlaySessionEnded.bind(socketService),
    onPlaySessionStartConfirmed: socketService.onPlaySessionStartConfirmed.bind(socketService),
    onPlaySessionEndConfirmed: socketService.onPlaySessionEndConfirmed.bind(socketService),
    onNewPlayRequest: socketService.onNewPlayRequest.bind(socketService),
    onPlayRequestUpdate: socketService.onPlayRequestUpdate.bind(socketService),
    onNewMessage: socketService.onNewMessage.bind(socketService),
    onMessageSent: socketService.onMessageSent.bind(socketService),
    onBadgeEarned: socketService.onBadgeEarned.bind(socketService),
  };
};
