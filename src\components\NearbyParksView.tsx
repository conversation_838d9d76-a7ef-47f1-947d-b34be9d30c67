
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Users, Clock, Shield } from "lucide-react";

const NearbyParksView = () => {
  const [parks] = useState([
    {
      id: 1,
      name: "Maple Park",
      distance: "0.3 miles",
      kidsPresent: 4,
      ageGroups: ["6-8", "9-11"],
      safetyRating: 5,
      amenities: ["Playground", "Soccer Field", "Picnic Area"],
      currentCondition: "Perfect",
      weather: "Sunny, 72°F",
      lastUpdated: "5 mins ago"
    },
    {
      id: 2,
      name: "Sunny Playground",
      distance: "0.5 miles",
      kidsPresent: 2,
      ageGroups: ["4-6"],
      safetyRating: 4,
      amenities: ["Playground", "Sandbox", "Swings"],
      currentCondition: "Good",
      weather: "Partly Cloudy, 70°F",
      lastUpdated: "12 mins ago"
    },
    {
      id: 3,
      name: "Central Park",
      distance: "0.2 miles",
      kidsPresent: 7,
      ageGroups: ["5-7", "8-10", "11-13"],
      safetyRating: 5,
      amenities: ["Playground", "Basketball Court", "Walking Trails", "Duck Pond"],
      currentCondition: "Excellent",
      weather: "Sunny, 74°F",
      lastUpdated: "2 mins ago"
    },
    {
      id: 4,
      name: "Riverside Recreation",
      distance: "0.8 miles",
      kidsPresent: 1,
      ageGroups: ["7-9"],
      safetyRating: 4,
      amenities: ["Large Playground", "Splash Pad", "Sports Courts"],
      currentCondition: "Good",
      weather: "Sunny, 73°F",
      lastUpdated: "8 mins ago"
    }
  ]);

  const getConditionColor = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'perfect': return 'bg-emerald-100 text-emerald-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold">Nearby Parks</h2>
        <p className="text-gray-600">See which parks have kids playing right now</p>
      </div>

      {/* Live Status Banner */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <div>
                <div className="font-medium text-blue-900">Live Park Activity</div>
                <div className="text-sm text-blue-700">Updates every 5 minutes • Last update: 2 mins ago</div>
              </div>
            </div>
            <Button size="sm" variant="outline" className="border-blue-300">
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Parks Grid */}
      <div className="grid md:grid-cols-2 gap-6">
        {parks.map((park) => (
          <Card key={park.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{park.name}</CardTitle>
                  <CardDescription className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    {park.distance} away
                  </CardDescription>
                </div>
                <Badge className={getConditionColor(park.currentCondition)}>
                  {park.currentCondition}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Kids Present */}
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    <span className="font-medium">Kids Playing Now</span>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">{park.kidsPresent}</div>
                    <div className="text-xs text-gray-600">Ages: {park.ageGroups.join(", ")}</div>
                  </div>
                </div>

                {/* Safety & Weather */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-2 bg-green-50 rounded">
                    <Shield className="h-5 w-5 text-green-600 mx-auto mb-1" />
                    <div className="text-sm font-medium">Safety</div>
                    <div className="text-xs text-gray-600">{park.safetyRating}/5 ⭐</div>
                  </div>
                  <div className="text-center p-2 bg-yellow-50 rounded">
                    <span className="text-xl mb-1 block">🌤️</span>
                    <div className="text-sm font-medium">Weather</div>
                    <div className="text-xs text-gray-600">{park.weather}</div>
                  </div>
                </div>

                {/* Amenities */}
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-2">Amenities</div>
                  <div className="flex flex-wrap gap-1">
                    {park.amenities.map((amenity, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700">
                    Send Child Here
                  </Button>
                  <Button size="sm" variant="outline">
                    Get Directions
                  </Button>
                </div>

                {/* Last Updated */}
                <div className="text-xs text-gray-500 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Updated {park.lastUpdated}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Safety Notice */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Shield className="h-6 w-6 text-green-600 mt-1" />
            <div>
              <div className="font-medium text-green-900 mb-1">Safety Features Active</div>
              <div className="text-sm text-green-700">
                All parks are geo-fenced safe zones. Parents will be notified when children 
                arrive and depart. Emergency contacts are always available.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NearbyParksView;
