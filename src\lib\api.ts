import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  createdAt: string;
  children?: Child[];
}

export interface Child {
  id: string;
  name: string;
  age: number;
  interests: string[];
  avatar?: string;
  status: 'AVAILABLE' | 'PLAYING' | 'OFFLINE';
  currentLocation?: string;
  parentId: string;
  createdAt: string;
}

export interface Park {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  amenities: string[];
  safetyRating: number;
  description?: string;
  distance?: number;
  currentActivity?: number;
  activeChildren?: Child[];
  ageGroups?: string[];
}

export interface PlayRequest {
  id: string;
  senderId: string;
  receiverId: string;
  childSenderId: string;
  childReceiverId: string;
  parkId: string;
  suggestedTime: string;
  duration: number;
  suggestedGame?: string;
  message?: string;
  status: 'PENDING' | 'APPROVED' | 'DECLINED' | 'EXPIRED';
  createdAt: string;
  sender?: User;
  receiver?: User;
  childSender?: Child;
  childReceiver?: Child;
  park?: Park;
}

export interface Game {
  id: string;
  name: string;
  emoji: string;
  description: string;
  instructions: string[];
  minPlayers: number;
  maxPlayers: number;
  minAge: number;
  maxAge: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  equipment: string[];
  duration: number;
  popularity: number;
  playCount?: number;
}

export interface Badge {
  id: string;
  name: string;
  emoji: string;
  description: string;
  category: 'FRIENDSHIP' | 'ADVENTURE' | 'GAMES' | 'ACTIVITY' | 'LEADERSHIP' | 'SAFETY';
  rarity: 'COMMON' | 'RARE' | 'EPIC' | 'LEGENDARY';
  criteria: any;
}

export interface UserBadge {
  id: string;
  childId: string;
  badgeId: string;
  progress: number;
  total: number;
  earned: boolean;
  earnedAt?: string;
  badge: Badge;
}

// Auth API
export const authAPI = {
  register: async (data: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }) => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  login: async (data: { email: string; password: string }) => {
    const response = await api.post('/auth/login', data);
    return response.data;
  },

  getProfile: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  refreshToken: async () => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },
};

// Users API
export const usersAPI = {
  getProfile: async () => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  updateProfile: async (data: Partial<User>) => {
    const response = await api.put('/users/profile', data);
    return response.data;
  },

  changePassword: async (data: { currentPassword: string; newPassword: string }) => {
    const response = await api.put('/users/password', data);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/users/stats');
    return response.data;
  },

  deleteAccount: async (password: string) => {
    const response = await api.delete('/users/account', { data: { password } });
    return response.data;
  },
};

// Children API
export const childrenAPI = {
  getAll: async () => {
    const response = await api.get('/children');
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/children/${id}`);
    return response.data;
  },

  create: async (data: {
    name: string;
    age: number;
    interests: string[];
    avatar?: string;
  }) => {
    const response = await api.post('/children', data);
    return response.data;
  },

  update: async (id: string, data: Partial<Child>) => {
    const response = await api.put(`/children/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/children/${id}`);
    return response.data;
  },

  getNearby: async (childId: string, latitude: number, longitude: number, radius?: number) => {
    const response = await api.get(`/children/${childId}/nearby`, {
      params: { latitude, longitude, radius }
    });
    return response.data;
  },
};

// Parks API
export const parksAPI = {
  getAll: async () => {
    const response = await api.get('/parks');
    return response.data;
  },

  getNearby: async (latitude: number, longitude: number, radius?: number) => {
    const response = await api.post('/parks/nearby', { latitude, longitude, radius });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/parks/${id}`);
    return response.data;
  },

  getActivity: async (id: string, days?: number) => {
    const response = await api.get(`/parks/${id}/activity`, { params: { days } });
    return response.data;
  },
};

// Play Requests API
export const playRequestsAPI = {
  getAll: async (status?: string, type?: 'sent' | 'received' | 'all') => {
    const response = await api.get('/play-requests', { params: { status, type } });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/play-requests/${id}`);
    return response.data;
  },

  create: async (data: {
    childReceiverId: string;
    childSenderId: string;
    parkId: string;
    suggestedTime: string;
    duration: number;
    suggestedGame?: string;
    message?: string;
  }) => {
    const response = await api.post('/play-requests', data);
    return response.data;
  },

  updateStatus: async (id: string, status: 'APPROVED' | 'DECLINED') => {
    const response = await api.put(`/play-requests/${id}`, { status });
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/play-requests/${id}`);
    return response.data;
  },
};

// Games API
export const gamesAPI = {
  getAll: async (filters?: {
    difficulty?: string;
    minAge?: number;
    maxAge?: number;
    minPlayers?: number;
    maxPlayers?: number;
  }) => {
    const response = await api.get('/games', { params: filters });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/games/${id}`);
    return response.data;
  },

  getSuggestions: async (childIds: string[], playerCount?: number) => {
    const response = await api.post('/games/suggestions', { childIds, playerCount });
    return response.data;
  },

  markAsPlayed: async (id: string) => {
    const response = await api.post(`/games/${id}/played`);
    return response.data;
  },
};

// Badges API
export const badgesAPI = {
  getAll: async (category?: string, rarity?: string) => {
    const response = await api.get('/badges', { params: { category, rarity } });
    return response.data;
  },

  getForChild: async (childId: string) => {
    const response = await api.get(`/badges/child/${childId}`);
    return response.data;
  },

  updateProgress: async (childId: string, badgeId: string, increment?: number) => {
    const response = await api.post(`/badges/child/${childId}/progress`, { badgeId, increment });
    return response.data;
  },

  getLeaderboard: async (badgeId: string) => {
    const response = await api.get(`/badges/${badgeId}/leaderboard`);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/badges/stats');
    return response.data;
  },
};

// Uploads API
export const uploadsAPI = {
  uploadAvatar: async (file: File) => {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await api.post('/uploads/avatars', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadFile: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/uploads/general', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deleteFile: async (type: 'avatars' | 'general', filename: string) => {
    const response = await api.delete(`/uploads/${type}/${filename}`);
    return response.data;
  },
};

export default api;
