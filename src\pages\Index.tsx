
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Shield, Users, Gamepad, Clock } from "lucide-react";
import ParentDashboard from "@/components/ParentDashboard";
import KidsInterface from "@/components/KidsInterface";

const Index = () => {
  const [currentView, setCurrentView] = useState<'landing' | 'parent' | 'kids'>('landing');

  if (currentView === 'parent') {
    return <ParentDashboard onBack={() => setCurrentView('landing')} />;
  }

  if (currentView === 'kids') {
    return <KidsInterface onBack={() => setCurrentView('landing')} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-green-600 mb-4">
            🌳 OutPlay
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 mb-6">
            Connect neighboring kids for outdoor fun!
          </p>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            Turn screen time into play time. Help your children discover nearby playmates 
            and enjoy safe, supervised outdoor activities in local parks.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={() => setCurrentView('parent')}
              size="lg" 
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3"
            >
              👨‍👩‍👧‍👦 Parent Dashboard
            </Button>
            <Button 
              onClick={() => setCurrentView('kids')}
              size="lg" 
              variant="outline" 
              className="border-green-600 text-green-600 hover:bg-green-50 px-8 py-3"
            >
              🎮 Kids Play Zone
            </Button>
          </div>
        </div>

        {/* Core Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <MapPin className="h-12 w-12 text-green-600 mx-auto mb-2" />
              <CardTitle className="text-lg">Park Matchmaking</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Find playmates available right now in nearby parks</p>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <Users className="h-12 w-12 text-blue-600 mx-auto mb-2" />
              <CardTitle className="text-lg">Age Filtering</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Kids matched by appropriate age groups for safe play</p>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <Shield className="h-12 w-12 text-purple-600 mx-auto mb-2" />
              <CardTitle className="text-lg">Parental Control</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Parents manage profiles and approve all play requests</p>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <Gamepad className="h-12 w-12 text-orange-600 mx-auto mb-2" />
              <CardTitle className="text-lg">Game Suggestions</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Discover group games like tag, hide-and-seek, and more</p>
            </CardContent>
          </Card>
        </div>

        {/* Key Features List */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-green-600">🎯 Core Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-lg mb-3 text-gray-800">👨‍👩‍👧‍👦 For Parents:</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Create & manage child profiles (age, interests)</li>
                  <li>• Approve friend and play requests</li>
                  <li>• Set playtime availability schedules</li>
                  <li>• See nearby kids at parks (geo-enabled)</li>
                  <li>• Safety alerts and trusted contacts</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-3 text-gray-800">🧒 For Kids:</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Choose games to play with friends</li>
                  <li>• See nearby kids "Ready to Play"</li>
                  <li>• Join park games and events</li>
                  <li>• Collect badges for outdoor activities</li>
                  <li>• Safe, supervised interactions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bonus Features */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-blue-600">✨ Bonus Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h4 className="font-semibold mb-2">Activity Tracking</h4>
                <p className="text-sm text-gray-600">Log outdoor time to reduce screen time</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <MapPin className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <h4 className="font-semibold mb-2">Live Park View</h4>
                <p className="text-sm text-gray-600">"Who's at the park?" with safety filters</p>
              </div>
              
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <span className="text-2xl block mb-2">🌤️</span>
                <h4 className="font-semibold mb-2">Weather Updates</h4>
                <p className="text-sm text-gray-600">Park conditions and weather alerts</p>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <span className="text-2xl block mb-2">🏆</span>
                <h4 className="font-semibold mb-2">Outdoor Badges</h4>
                <p className="text-sm text-gray-600">Gamified incentives for outdoor play</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Safety Message */}
        <Card className="bg-green-50 border-green-200">
          <CardContent className="text-center py-8">
            <Shield className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-green-800 mb-2">Safety First</h3>
            <p className="text-green-700 max-w-2xl mx-auto">
              OutPlay prioritizes child safety with geo-fenced safe zones, parental supervision, 
              and trusted contact networks. Every interaction is monitored and approved by parents.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
