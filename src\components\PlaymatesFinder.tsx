
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, MapPin, Clock, Star } from "lucide-react";

const PlaymatesFinder = () => {
  const [availableKids] = useState([
    {
      id: 1,
      name: "<PERSON>",
      age: 7,
      location: "Maple Park",
      distance: "0.3 miles",
      interests: ["Tag", "Soccer"],
      status: "Ready to Play",
      playTime: "Available now",
      friendshipLevel: "New friend"
    },
    {
      id: 2,
      name: "<PERSON>",
      age: 8,
      location: "Central Park",
      distance: "0.2 miles",
      interests: ["Basketball", "Tag"],
      status: "Playing now",
      playTime: "30 more minutes",
      friendshipLevel: "Good friend"
    },
    {
      id: 3,
      name: "<PERSON>",
      age: 6,
      location: "Sunny Playground",
      distance: "0.5 miles",
      interests: ["Swings", "Hide and Seek"],
      status: "Ready to Play",
      playTime: "Available now",
      friendshipLevel: "Best friend"
    },
    {
      id: 4,
      name: "<PERSON>",
      age: 9,
      location: "Riverside Recreation",
      distance: "0.8 miles",
      interests: ["Soccer", "Bikes"],
      status: "Looking for friends",
      playTime: "Available for 1 hour",
      friendshipLevel: "New friend"
    }
  ]);

  const getFriendshipColor = (level: string) => {
    switch (level) {
      case 'Best friend': return 'bg-purple-100 text-purple-800';
      case 'Good friend': return 'bg-blue-100 text-blue-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ready to Play': return 'bg-green-100 text-green-800';
      case 'Playing now': return 'bg-blue-100 text-blue-800';
      case 'Looking for friends': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Fun Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-blue-600 mb-2">👫 Find Your Play Buddies!</h2>
        <p className="text-lg text-gray-600">See who's ready to play near you right now</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="text-center bg-green-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">4</div>
            <div className="text-sm text-green-700">Kids Available</div>
          </CardContent>
        </Card>
        <Card className="text-center bg-blue-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">3</div>
            <div className="text-sm text-blue-700">Parks Open</div>
          </CardContent>
        </Card>
        <Card className="text-center bg-purple-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">2</div>
            <div className="text-sm text-purple-700">Best Friends</div>
          </CardContent>
        </Card>
        <Card className="text-center bg-yellow-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">☀️</div>
            <div className="text-sm text-yellow-700">Perfect Weather</div>
          </CardContent>
        </Card>
      </div>

      {/* Available Playmates */}
      <div>
        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Users className="h-6 w-6 text-blue-600" />
          Friends Ready to Play
        </h3>
        
        <div className="grid md:grid-cols-2 gap-4">
          {availableKids.map((kid) => (
            <Card key={kid.id} className="hover:shadow-lg transition-all hover:scale-102 cursor-pointer">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <span className="text-2xl">
                        {kid.friendshipLevel === 'Best friend' ? '💜' : 
                         kid.friendshipLevel === 'Good friend' ? '💙' : '💚'}
                      </span>
                      {kid.name}, age {kid.age}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-1 mt-1">
                      <MapPin className="h-4 w-4" />
                      {kid.location} • {kid.distance}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(kid.status)}>
                    {kid.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Friendship Level */}
                  <Badge className={getFriendshipColor(kid.friendshipLevel)}>
                    {kid.friendshipLevel}
                  </Badge>

                  {/* Interests */}
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-2">Likes to play:</div>
                    <div className="flex flex-wrap gap-2">
                      {kid.interests.map((interest, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          🎯 {interest}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>{kid.playTime}</span>
                  </div>

                  {/* Action Button */}
                  <Button 
                    className="w-full bg-blue-600 hover:bg-blue-700 mt-4"
                    size="sm"
                  >
                    🤝 Ask to Play Together
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="text-center py-8">
          <div className="text-4xl mb-4">🌟</div>
          <h3 className="text-xl font-semibold text-blue-900 mb-2">Ready for Adventure?</h3>
          <p className="text-blue-700 mb-4">
            Ask your parent to help you connect with friends and start playing!
          </p>
          <Button className="bg-purple-600 hover:bg-purple-700">
            🏃‍♀️ Let's Go Play!
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default PlaymatesFinder;
