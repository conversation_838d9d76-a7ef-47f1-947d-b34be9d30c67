
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Clock, MapPin, Users } from "lucide-react";

const PlayRequestApproval = () => {
  const [requests] = useState([
    {
      id: 1,
      requestFrom: "<PERSON>",
      requestFor: "<PERSON>",
      childAge: 7,
      parentContact: "<EMAIL>",
      location: "Maple Park",
      suggestedTime: "Today 3:00 PM",
      duration: "1 hour",
      suggestedGame: "Tag and Soccer",
      status: "pending",
      distance: "0.3 miles away"
    },
    {
      id: 2,
      requestFrom: "<PERSON>",
      requestFor: "<PERSON>",
      childAge: 6,
      parentContact: "<EMAIL>",
      location: "Sunny Playground",
      suggestedTime: "Tomorrow 2:00 PM",
      duration: "45 minutes",
      suggestedGame: "Playground activities",
      status: "pending",
      distance: "0.5 miles away"
    },
    {
      id: 3,
      requestFrom: "<PERSON>",
      requestFor: "<PERSON>",
      childAge: 8,
      parentContact: "<EMAIL>",
      location: "Central Park",
      suggestedTime: "Yesterday 4:00 PM",
      duration: "1.5 hours",
      suggestedGame: "Hide and Seek",
      status: "approved",
      distance: "0.2 miles away"
    }
  ]);

  const pendingRequests = requests.filter(req => req.status === 'pending');
  const recentRequests = requests.filter(req => req.status !== 'pending');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold">Play Requests</h2>
        <p className="text-gray-600">Review and approve play requests for your children</p>
      </div>

      {/* Pending Requests */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Clock className="h-5 w-5 text-blue-600" />
          Pending Requests ({pendingRequests.length})
        </h3>
        
        {pendingRequests.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <div className="text-gray-500">No pending requests at the moment</div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {pendingRequests.map((request) => (
              <Card key={request.id} className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        Play request for {request.requestFor}
                      </CardTitle>
                      <CardDescription>
                        From {request.requestFrom} (Parent of {request.childAge}-year-old)
                      </CardDescription>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">New Request</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span>{request.location} • {request.distance}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span>{request.suggestedTime} • {request.duration}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span>Suggested activity: {request.suggestedGame}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="text-sm">
                        <span className="font-medium">Parent Contact:</span>
                        <div className="text-gray-600">{request.parentContact}</div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" className="bg-green-600 hover:bg-green-700">
                          <Check className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button size="sm" variant="outline">
                          Message Parent
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 border-red-200">
                          Decline
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Recent Activity */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Check className="h-5 w-5 text-green-600" />
          Recent Activity
        </h3>
        
        <div className="space-y-4">
          {recentRequests.map((request) => (
            <Card key={request.id} className="bg-gray-50">
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">{request.requestFor} played with {request.requestFrom}'s child</div>
                    <div className="text-sm text-gray-600">
                      {request.location} • {request.suggestedTime} • {request.duration}
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Completed</Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PlayRequestApproval;
