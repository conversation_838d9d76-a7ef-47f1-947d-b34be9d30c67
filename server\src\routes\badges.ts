import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all badges
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { category, rarity } = req.query;

  let whereClause: any = {};

  if (category) {
    whereClause.category = category;
  }

  if (rarity) {
    whereClause.rarity = rarity;
  }

  const badges = await prisma.badge.findMany({
    where: whereClause,
    include: {
      userBadges: {
        select: {
          id: true,
          earned: true
        }
      }
    },
    orderBy: [
      { category: 'asc' },
      { rarity: 'asc' }
    ]
  });

  // Add statistics to each badge
  const badgesWithStats = badges.map(badge => ({
    ...badge,
    totalEarned: badge.userBadges.filter(ub => ub.earned).length,
    totalUsers: badge.userBadges.length,
    userBadges: undefined
  }));

  res.json({ badges: badgesWithStats });
}));

// Get badges for a specific child
router.get('/child/:childId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { childId } = req.params;

  // Verify the child belongs to the authenticated user
  const child = await prisma.child.findFirst({
    where: {
      id: childId,
      parentId: req.user!.id
    }
  });

  if (!child) {
    throw createError('Child not found', 404);
  }

  const userBadges = await prisma.userBadge.findMany({
    where: { childId },
    include: {
      badge: true
    },
    orderBy: [
      { earned: 'desc' },
      { badge: { category: 'asc' } },
      { badge: { rarity: 'asc' } }
    ]
  });

  // Separate earned and unearned badges
  const earnedBadges = userBadges.filter(ub => ub.earned);
  const inProgressBadges = userBadges.filter(ub => !ub.earned);

  // Calculate statistics
  const stats = {
    totalEarned: earnedBadges.length,
    totalAvailable: userBadges.length,
    completionPercentage: Math.round((earnedBadges.length / userBadges.length) * 100),
    byCategory: userBadges.reduce((acc, ub) => {
      const category = ub.badge.category;
      if (!acc[category]) {
        acc[category] = { total: 0, earned: 0 };
      }
      acc[category].total++;
      if (ub.earned) acc[category].earned++;
      return acc;
    }, {} as Record<string, { total: number; earned: number }>),
    byRarity: userBadges.reduce((acc, ub) => {
      const rarity = ub.badge.rarity;
      if (!acc[rarity]) {
        acc[rarity] = { total: 0, earned: 0 };
      }
      acc[rarity].total++;
      if (ub.earned) acc[rarity].earned++;
      return acc;
    }, {} as Record<string, { total: number; earned: number }>)
  };

  res.json({
    earnedBadges,
    inProgressBadges,
    stats
  });
}));

// Update badge progress for a child
router.post('/child/:childId/progress', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { childId } = req.params;
  const { badgeId, increment = 1 } = req.body;

  // Verify the child belongs to the authenticated user
  const child = await prisma.child.findFirst({
    where: {
      id: childId,
      parentId: req.user!.id
    }
  });

  if (!child) {
    throw createError('Child not found', 404);
  }

  // Get the user badge
  const userBadge = await prisma.userBadge.findUnique({
    where: {
      childId_badgeId: {
        childId,
        badgeId
      }
    },
    include: {
      badge: true
    }
  });

  if (!userBadge) {
    throw createError('Badge not found for this child', 404);
  }

  if (userBadge.earned) {
    return res.json({
      message: 'Badge already earned',
      userBadge
    });
  }

  // Update progress
  const newProgress = Math.min(userBadge.progress + increment, userBadge.total);
  const isEarned = newProgress >= userBadge.total;

  const updatedUserBadge = await prisma.userBadge.update({
    where: {
      childId_badgeId: {
        childId,
        badgeId
      }
    },
    data: {
      progress: newProgress,
      earned: isEarned,
      earnedAt: isEarned ? new Date() : null
    },
    include: {
      badge: true
    }
  });

  res.json({
    message: isEarned ? 'Badge earned!' : 'Progress updated',
    userBadge: updatedUserBadge,
    newlyEarned: isEarned
  });
}));

// Get leaderboard for a specific badge
router.get('/:badgeId/leaderboard', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { badgeId } = req.params;

  const badge = await prisma.badge.findUnique({
    where: { id: badgeId }
  });

  if (!badge) {
    throw createError('Badge not found', 404);
  }

  const leaderboard = await prisma.userBadge.findMany({
    where: {
      badgeId,
      earned: true
    },
    include: {
      child: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true
        }
      }
    },
    orderBy: { earnedAt: 'asc' },
    take: 50
  });

  res.json({
    badge,
    leaderboard: leaderboard.map((ub, index) => ({
      rank: index + 1,
      child: ub.child,
      earnedAt: ub.earnedAt,
      progress: ub.progress,
      total: ub.total
    }))
  });
}));

// Get badge statistics
router.get('/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const totalBadges = await prisma.badge.count();
  const totalUserBadges = await prisma.userBadge.count();
  const totalEarnedBadges = await prisma.userBadge.count({
    where: { earned: true }
  });

  const badgesByCategory = await prisma.badge.groupBy({
    by: ['category'],
    _count: { id: true }
  });

  const badgesByRarity = await prisma.badge.groupBy({
    by: ['rarity'],
    _count: { id: true }
  });

  const mostEarnedBadges = await prisma.userBadge.groupBy({
    by: ['badgeId'],
    where: { earned: true },
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
    take: 10
  });

  // Get badge details for most earned
  const mostEarnedBadgeIds = mostEarnedBadges.map(mb => mb.badgeId);
  const badgeDetails = await prisma.badge.findMany({
    where: { id: { in: mostEarnedBadgeIds } }
  });

  const mostEarnedWithDetails = mostEarnedBadges.map(mb => ({
    badge: badgeDetails.find(b => b.id === mb.badgeId),
    earnedCount: mb._count.id
  }));

  res.json({
    overview: {
      totalBadges,
      totalUserBadges,
      totalEarnedBadges,
      completionRate: Math.round((totalEarnedBadges / totalUserBadges) * 100)
    },
    distribution: {
      byCategory: badgesByCategory.reduce((acc, item) => {
        acc[item.category] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
      byRarity: badgesByRarity.reduce((acc, item) => {
        acc[item.rarity] = item._count.id;
        return acc;
      }, {} as Record<string, number>)
    },
    mostEarned: mostEarnedWithDetails
  });
}));

export default router;
