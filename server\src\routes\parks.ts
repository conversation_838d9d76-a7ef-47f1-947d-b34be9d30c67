import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { validate, createParkSchema, locationSchema } from '../validation/schemas.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all parks
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const parks = await prisma.park.findMany({
    include: {
      playSessions: {
        where: {
          endTime: null // Currently active sessions
        },
        include: {
          child: {
            select: {
              id: true,
              name: true,
              age: true,
              avatar: true
            }
          }
        }
      }
    }
  });

  // Add current activity count to each park
  const parksWithActivity = parks.map(park => ({
    ...park,
    currentActivity: park.playSessions.length,
    activeChildren: park.playSessions.map(session => session.child)
  }));

  res.json({ parks: parksWithActivity });
}));

// Get nearby parks based on location
router.post('/nearby', validate(locationSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { latitude, longitude, radius } = req.body;

  // For now, return all parks since we need proper geolocation calculations
  // In production, you'd use PostGIS or similar for proper geo queries
  const parks = await prisma.park.findMany({
    include: {
      playSessions: {
        where: {
          endTime: null // Currently active sessions
        },
        include: {
          child: {
            select: {
              id: true,
              name: true,
              age: true,
              avatar: true,
              status: true
            }
          }
        }
      }
    }
  });

  // Calculate distance (mock calculation for now)
  const parksWithDistance = parks.map(park => {
    // Mock distance calculation - in production use proper geolocation
    const distance = Math.random() * radius;
    
    return {
      ...park,
      distance: parseFloat(distance.toFixed(1)),
      currentActivity: park.playSessions.length,
      activeChildren: park.playSessions.map(session => session.child),
      ageGroups: getAgeGroups(park.playSessions.map(s => s.child.age))
    };
  }).filter(park => park.distance <= radius)
    .sort((a, b) => a.distance - b.distance);

  res.json({ parks: parksWithDistance });
}));

// Get a specific park
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const park = await prisma.park.findUnique({
    where: { id },
    include: {
      playSessions: {
        include: {
          child: {
            select: {
              id: true,
              name: true,
              age: true,
              avatar: true,
              status: true,
              interests: true
            }
          },
          game: true
        },
        orderBy: { startTime: 'desc' }
      }
    }
  });

  if (!park) {
    throw createError('Park not found', 404);
  }

  const activeSessions = park.playSessions.filter(session => !session.endTime);
  const recentSessions = park.playSessions.filter(session => session.endTime).slice(0, 10);

  res.json({
    park: {
      ...park,
      currentActivity: activeSessions.length,
      activeChildren: activeSessions.map(session => session.child),
      recentActivity: recentSessions,
      ageGroups: getAgeGroups(activeSessions.map(s => s.child.age))
    }
  });
}));

// Create a new park (admin only - for now any authenticated user)
router.post('/', validate(createParkSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { name, address, latitude, longitude, amenities, safetyRating, description } = req.body;

  const park = await prisma.park.create({
    data: {
      name,
      address,
      latitude,
      longitude,
      amenities,
      safetyRating,
      description
    }
  });

  res.status(201).json({
    message: 'Park created successfully',
    park
  });
}));

// Update park information
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const park = await prisma.park.update({
    where: { id },
    data: updateData
  });

  res.json({
    message: 'Park updated successfully',
    park
  });
}));

// Get park activity history
router.get('/:id/activity', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { days = 7 } = req.query;

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - Number(days));

  const activity = await prisma.playSession.findMany({
    where: {
      parkId: id,
      startTime: {
        gte: startDate
      }
    },
    include: {
      child: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true
        }
      },
      game: true
    },
    orderBy: { startTime: 'desc' }
  });

  // Group activity by day
  const activityByDay = activity.reduce((acc, session) => {
    const day = session.startTime.toISOString().split('T')[0];
    if (!acc[day]) {
      acc[day] = [];
    }
    acc[day].push(session);
    return acc;
  }, {} as Record<string, typeof activity>);

  res.json({
    activity: activityByDay,
    totalSessions: activity.length,
    uniqueChildren: new Set(activity.map(s => s.child.id)).size
  });
}));

// Helper function to group ages
function getAgeGroups(ages: number[]): string[] {
  const groups = new Set<string>();
  
  ages.forEach(age => {
    if (age >= 3 && age <= 5) groups.add('3-5');
    else if (age >= 6 && age <= 8) groups.add('6-8');
    else if (age >= 9 && age <= 11) groups.add('9-11');
    else if (age >= 12 && age <= 14) groups.add('12-14');
    else if (age >= 15 && age <= 17) groups.add('15-17');
  });
  
  return Array.from(groups).sort();
}

export default router;
