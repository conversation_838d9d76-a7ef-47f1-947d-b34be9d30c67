// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  phone     String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  children     Child[]
  sentRequests PlayRequest[] @relation("RequestSender")
  receivedRequests PlayRequest[] @relation("RequestReceiver")
  
  @@map("users")
}

model Child {
  id          String   @id @default(cuid())
  name        String
  age         Int
  interests   String[] // Array of interests
  avatar      String?
  status      ChildStatus @default(AVAILABLE)
  currentLocation String?
  parentId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent       User @relation(fields: [parentId], references: [id], onDelete: Cascade)
  sentRequests PlayRequest[] @relation("ChildSender")
  receivedRequests PlayRequest[] @relation("ChildReceiver")
  friendships1 Friendship[] @relation("Child1")
  friendships2 Friendship[] @relation("Child2")
  badges       UserBadge[]
  playSessions PlaySession[]
  
  @@map("children")
}

model Park {
  id          String   @id @default(cuid())
  name        String
  address     String
  latitude    Float
  longitude   Float
  amenities   String[] // Array of amenities
  safetyRating Int     @default(5)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  playRequests PlayRequest[]
  playSessions PlaySession[]
  
  @@map("parks")
}

model PlayRequest {
  id            String   @id @default(cuid())
  senderId      String
  receiverId    String
  childSenderId String
  childReceiverId String
  parkId        String
  suggestedTime DateTime
  duration      Int      // Duration in minutes
  suggestedGame String?
  message       String?
  status        RequestStatus @default(PENDING)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  sender       User @relation("RequestSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver     User @relation("RequestReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  childSender  Child @relation("ChildSender", fields: [childSenderId], references: [id], onDelete: Cascade)
  childReceiver Child @relation("ChildReceiver", fields: [childReceiverId], references: [id], onDelete: Cascade)
  park         Park @relation(fields: [parkId], references: [id], onDelete: Cascade)
  
  @@map("play_requests")
}

model Friendship {
  id        String   @id @default(cuid())
  child1Id  String
  child2Id  String
  status    FriendshipStatus @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  child1 Child @relation("Child1", fields: [child1Id], references: [id], onDelete: Cascade)
  child2 Child @relation("Child2", fields: [child2Id], references: [id], onDelete: Cascade)
  
  @@unique([child1Id, child2Id])
  @@map("friendships")
}

model Game {
  id           String   @id @default(cuid())
  name         String   @unique
  emoji        String
  description  String
  instructions String[]
  minPlayers   Int
  maxPlayers   Int
  minAge       Int
  maxAge       Int
  difficulty   GameDifficulty
  equipment    String[]
  duration     Int      // Duration in minutes
  popularity   Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  playSessions PlaySession[]
  
  @@map("games")
}

model Badge {
  id          String   @id @default(cuid())
  name        String   @unique
  emoji       String
  description String
  category    BadgeCategory
  rarity      BadgeRarity
  criteria    Json     // Flexible criteria for earning the badge
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userBadges UserBadge[]
  
  @@map("badges")
}

model UserBadge {
  id        String   @id @default(cuid())
  childId   String
  badgeId   String
  progress  Int      @default(0)
  total     Int
  earned    Boolean  @default(false)
  earnedAt  DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  child Child @relation(fields: [childId], references: [id], onDelete: Cascade)
  badge Badge @relation(fields: [badgeId], references: [id], onDelete: Cascade)
  
  @@unique([childId, badgeId])
  @@map("user_badges")
}

model PlaySession {
  id        String   @id @default(cuid())
  childId   String
  parkId    String
  gameId    String?
  startTime DateTime
  endTime   DateTime?
  duration  Int?     // Duration in minutes
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  child Child @relation(fields: [childId], references: [id], onDelete: Cascade)
  park  Park @relation(fields: [parkId], references: [id], onDelete: Cascade)
  game  Game? @relation(fields: [gameId], references: [id], onDelete: SetNull)
  
  @@map("play_sessions")
}

// Enums
enum ChildStatus {
  AVAILABLE
  PLAYING
  OFFLINE
}

enum RequestStatus {
  PENDING
  APPROVED
  DECLINED
  EXPIRED
}

enum FriendshipStatus {
  PENDING
  ACCEPTED
  BLOCKED
}

enum GameDifficulty {
  EASY
  MEDIUM
  HARD
}

enum BadgeCategory {
  FRIENDSHIP
  ADVENTURE
  GAMES
  ACTIVITY
  LEADERSHIP
  SAFETY
}

enum BadgeRarity {
  COMMON
  RARE
  EPIC
  LEGENDARY
}
