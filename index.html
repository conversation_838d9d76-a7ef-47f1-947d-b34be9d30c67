<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />

    <!-- PWA Meta Tags -->
    <title>OutPlay - Kids Outdoor Playtime</title>
    <meta
      name="description"
      content="Connect neighboring kids for outdoor fun and safe playtime activities"
    />
    <meta name="theme-color" content="#16a34a" />
    <meta name="background-color" content="#ffffff" />

    <!-- Apple PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="OutPlay" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />

    <!-- Microsoft PWA Meta Tags -->
    <meta name="msapplication-TileColor" content="#16a34a" />
    <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />

    <!-- Preconnect to API -->
    <link rel="preconnect" href="http://localhost:3001" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="OutPlay - Kids Outdoor Playtime" />
    <meta
      property="og:description"
      content="Connect neighboring kids for outdoor fun and safe playtime activities"
    />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/icons/icon-512x512.png" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="OutPlay - Kids Outdoor Playtime" />
    <meta
      name="twitter:description"
      content="Connect neighboring kids for outdoor fun and safe playtime activities"
    />
    <meta name="twitter:image" content="/icons/icon-512x512.png" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Service Worker Registration -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("/sw.js")
            .then((registration) => {
              console.log("SW registered: ", registration);
            })
            .catch((registrationError) => {
              console.log("SW registration failed: ", registrationError);
            });
        });
      }
    </script>
  </body>
</html>
