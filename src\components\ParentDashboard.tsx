
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUp, Plus, MapPin, Users, Clock, Shield } from "lucide-react";
import ChildProfileManager from "./ChildProfileManager";
import PlayRequestApproval from "./PlayRequestApproval";
import NearbyParksView from "./NearbyParksView";

interface ParentDashboardProps {
  onBack: () => void;
}

const ParentDashboard = ({ onBack }: ParentDashboardProps) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'profiles' | 'requests' | 'parks'>('overview');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profiles':
        return <ChildProfileManager />;
      case 'requests':
        return <PlayRequestApproval />;
      case 'parks':
        return <NearbyParksView />;
      default:
        return (
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">2</div>
                  <div className="text-sm text-gray-600">Active Children</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">3</div>
                  <div className="text-sm text-gray-600">Pending Requests</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">5</div>
                  <div className="text-sm text-gray-600">Nearby Parks</div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <div className="font-medium">Emma played at Maple Park</div>
                      <div className="text-sm text-gray-600">2 hours ago • Duration: 45 mins</div>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Completed</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <div className="font-medium">New play request from Sarah (age 7)</div>
                      <div className="text-sm text-gray-600">1 hour ago • Wants to play with Emma</div>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">Pending</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <div className="font-medium">Weather Alert: Sunny Park</div>
                      <div className="text-sm text-gray-600">30 mins ago • Perfect conditions for outdoor play</div>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">Info</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid sm:grid-cols-2 gap-4">
                  <Button 
                    onClick={() => setActiveTab('profiles')}
                    className="h-20 flex flex-col gap-2"
                    variant="outline"
                  >
                    <Plus className="h-6 w-6" />
                    <span>Add Child Profile</span>
                  </Button>
                  
                  <Button 
                    onClick={() => setActiveTab('parks')}
                    className="h-20 flex flex-col gap-2"
                    variant="outline"
                  >
                    <MapPin className="h-6 w-6" />
                    <span>Check Nearby Parks</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button onClick={onBack} variant="ghost" size="sm">
                <ArrowUp className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-green-600">👨‍👩‍👧‍👦 Parent Dashboard</h1>
                <p className="text-gray-600">Manage your children's outdoor play activities</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: '📊' },
              { id: 'profiles', label: 'Child Profiles', icon: '👧👦' },
              { id: 'requests', label: 'Play Requests', icon: '🤝' },
              { id: 'parks', label: 'Nearby Parks', icon: '🌳' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-2 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default ParentDashboard;
