import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';

const router = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../../uploads');
const avatarsDir = path.join(uploadsDir, 'avatars');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

if (!fs.existsSync(avatarsDir)) {
  fs.mkdirSync(avatarsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadType = req.params.type || 'general';
    let destDir = uploadsDir;
    
    switch (uploadType) {
      case 'avatars':
        destDir = avatarsDir;
        break;
      default:
        destDir = uploadsDir;
    }
    
    cb(null, destDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  }
});

// File filter for images only
const fileFilter = (req: any, file: any, cb: any) => {
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Only image files are allowed (jpeg, jpg, png, gif, webp)'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB default
    files: 1 // Only one file at a time
  }
});

// Upload avatar
router.post('/avatars', upload.single('avatar'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('No file uploaded', 400);
  }

  const fileUrl = `/uploads/avatars/${req.file.filename}`;

  res.json({
    message: 'Avatar uploaded successfully',
    file: {
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      url: fileUrl
    }
  });
}));

// Upload general files
router.post('/general', upload.single('file'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('No file uploaded', 400);
  }

  const fileUrl = `/uploads/${req.file.filename}`;

  res.json({
    message: 'File uploaded successfully',
    file: {
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      url: fileUrl
    }
  });
}));

// Delete uploaded file
router.delete('/:type/:filename', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { type, filename } = req.params;

  // Validate type
  if (!['avatars', 'general'].includes(type)) {
    throw createError('Invalid file type', 400);
  }

  // Validate filename to prevent directory traversal
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw createError('Invalid filename', 400);
  }

  let filePath: string;
  switch (type) {
    case 'avatars':
      filePath = path.join(avatarsDir, filename);
      break;
    default:
      filePath = path.join(uploadsDir, filename);
  }

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    throw createError('File not found', 404);
  }

  // Delete file
  fs.unlinkSync(filePath);

  res.json({
    message: 'File deleted successfully'
  });
}));

// Get file info
router.get('/:type/:filename/info', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { type, filename } = req.params;

  // Validate type
  if (!['avatars', 'general'].includes(type)) {
    throw createError('Invalid file type', 400);
  }

  // Validate filename
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw createError('Invalid filename', 400);
  }

  let filePath: string;
  switch (type) {
    case 'avatars':
      filePath = path.join(avatarsDir, filename);
      break;
    default:
      filePath = path.join(uploadsDir, filename);
  }

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    throw createError('File not found', 404);
  }

  // Get file stats
  const stats = fs.statSync(filePath);

  res.json({
    filename,
    size: stats.size,
    created: stats.birthtime,
    modified: stats.mtime,
    url: `/uploads/${type === 'avatars' ? 'avatars/' : ''}${filename}`
  });
}));

// Error handling for multer
router.use((error: any, req: any, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File too large',
        maxSize: process.env.MAX_FILE_SIZE || '5MB'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'Too many files'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'Unexpected field name'
      });
    }
  }

  if (error.message.includes('Only image files are allowed')) {
    return res.status(400).json({
      error: 'Only image files are allowed (jpeg, jpg, png, gif, webp)'
    });
  }

  next(error);
});

export default router;
