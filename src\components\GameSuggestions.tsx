import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Clock, Star } from "lucide-react";

const GameSuggestions = () => {
  const [games] = useState([
    {
      id: 1,
      name: "Tag",
      emoji: "🏃‍♀️",
      players: "3-8 kids",
      duration: "15-30 minutes",
      ageGroup: "5-12",
      difficulty: "Easy",
      equipment: "None needed",
      description: "Classic chase game where one person is 'it' and tries to tag others!",
      instructions: [
        "Choose who is 'it' first",
        "Set boundaries for the play area",
        "The person who is 'it' counts to 10",
        "Run and try not to get tagged!",
        "When tagged, you become 'it'"
      ],
      popularity: 5
    },
    {
      id: 2,
      name: "<PERSON><PERSON> and Seek",
      emoji: "🙈",
      players: "3-10 kids",
      duration: "20-40 minutes",
      ageGroup: "4-12",
      difficulty: "Easy",
      equipment: "None needed",
      description: "Find the best hiding spots while the seeker counts!",
      instructions: [
        "Choose who seeks first",
        "Seeker counts to 20 with eyes closed",
        "Everyone else finds hiding spots",
        "See<PERSON> tries to find everyone",
        "First found becomes next seeker"
      ],
      popularity: 5
    },
    {
      id: 3,
      name: "Red Light, Green Light",
      emoji: "🚦",
      players: "4-15 kids",
      duration: "10-20 minutes",
      ageGroup: "4-10",
      difficulty: "Easy",
      equipment: "None needed",
      description: "Move when it's green light, freeze when it's red light!",
      instructions: [
        "One person is the traffic light",
        "Others line up at starting line",
        "Move forward on 'Green Light'",
        "Freeze immediately on 'Red Light'",
        "First to reach traffic light wins"
      ],
      popularity: 4
    },
    {
      id: 4,
      name: "Soccer Fun",
      emoji: "⚽",
      players: "4-12 kids",
      duration: "30-60 minutes",
      ageGroup: "6-14",
      difficulty: "Medium",
      equipment: "Soccer ball",
      description: "Kick the ball and score goals with your team!",
      instructions: [
        "Split into two teams",
        "Set up goals (can use cones)",
        "Try to kick ball into other team's goal",
        "No hands allowed (except goalkeeper)",
        "Most goals wins!"
      ],
      popularity: 4
    },
    {
      id: 5,
      name: "Duck Duck Goose",
      emoji: "🦆",
      players: "6-15 kids",
      duration: "15-30 minutes",
      ageGroup: "4-10",
      difficulty: "Easy",
      equipment: "None needed",
      description: "Sit in a circle and try to catch the goose!",
      instructions: [
        "Everyone sits in a circle",
        "One person walks around tapping heads",
        "Say 'duck' for each tap",
        "Say 'goose' to choose someone to chase you",
        "Try to sit in their spot before being tagged"
      ],
      popularity: 4
    },
    {
      id: 6,
      name: "Capture the Flag",
      emoji: "🏴",
      players: "6-20 kids",
      duration: "20-45 minutes",
      ageGroup: "7-14",
      difficulty: "Hard",
      equipment: "2 flags or markers",
      description: "Work as a team to capture the other team's flag!",
      instructions: [
        "Split into two teams",
        "Each team gets a flag in their territory",
        "Try to get the other team's flag",
        "If tagged in enemy territory, go to 'jail'",
        "First team to capture flag wins"
      ],
      popularity: 5
    }
  ]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (count: number) => {
    return '⭐'.repeat(count);
  };

  return (
    <div className="space-y-6">
      {/* Fun Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-green-600 mb-2">🎯 Fun Games to Play!</h2>
        <p className="text-lg text-gray-600">Pick a game and have amazing outdoor adventures</p>
      </div>

      {/* Game Categories */}
      <div className="flex flex-wrap gap-2 justify-center">
        <Badge className="bg-green-100 text-green-800 text-sm py-2 px-4">🏃‍♀️ Running Games</Badge>
        <Badge className="bg-blue-100 text-blue-800 text-sm py-2 px-4">⚽ Ball Games</Badge>
        <Badge className="bg-purple-100 text-purple-800 text-sm py-2 px-4">🧠 Strategy Games</Badge>
        <Badge className="bg-yellow-100 text-yellow-800 text-sm py-2 px-4">👥 Circle Games</Badge>
      </div>

      {/* Games Grid */}
      <div className="grid md:grid-cols-2 gap-6">
        {games.map((game) => (
          <Card key={game.id} className="hover:shadow-lg transition-all hover:scale-102 cursor-pointer">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl flex items-center gap-3">
                    <span className="text-3xl">{game.emoji}</span>
                    {game.name}
                  </CardTitle>
                  <CardDescription className="mt-2">
                    {game.description}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">Popularity</div>
                  <div className="text-lg">{renderStars(game.popularity)}</div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Game Details */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span>{game.players}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-green-600" />
                    <span>{game.duration}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Ages: </span>
                    <span className="font-medium">{game.ageGroup}</span>
                  </div>
                  <div>
                    <Badge className={getDifficultyColor(game.difficulty)}>
                      {game.difficulty}
                    </Badge>
                  </div>
                </div>

                {/* Equipment */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 mb-1">Equipment needed:</div>
                  <div className="text-sm text-gray-600">{game.equipment}</div>
                </div>

                {/* Instructions Preview */}
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-2">How to play:</div>
                  <div className="text-sm text-gray-600 space-y-1">
                    {game.instructions.slice(0, 2).map((instruction, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <span className="text-blue-600 font-bold">{index + 1}.</span>
                        <span>{instruction}</span>
                      </div>
                    ))}
                    {game.instructions.length > 2 && (
                      <div className="text-blue-600 text-xs">+{game.instructions.length - 2} more steps...</div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button className="flex-1 bg-green-600 hover:bg-green-700">
                    🎮 Let's Play This!
                  </Button>
                  <Button variant="outline" size="sm">
                    📖 Full Rules
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Suggestion Box */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardContent className="text-center py-8">
          <div className="text-4xl mb-4">💡</div>
          <h3 className="text-xl font-semibold text-purple-900 mb-2">Have a Game Idea?</h3>
          <p className="text-purple-700 mb-4">
            Know a fun game that's not here? Ask your parent to suggest it!
          </p>
          <Button className="bg-purple-600 hover:bg-purple-700">
            💌 Suggest a Game
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default GameSuggestions;
