import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export const setupSocketHandlers = (io: Server) => {
  // Authentication middleware for socket connections
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        return next(new Error('JWT secret not configured'));
      }

      const decoded = jwt.verify(token, jwtSecret) as { userId: string };
      
      // Verify user exists
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      });

      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user.id;
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Invalid authentication token'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User ${socket.user?.firstName} connected: ${socket.id}`);

    // Join user to their personal room for notifications
    socket.join(`user_${socket.userId}`);

    // Handle location updates
    socket.on('updateLocation', async (data) => {
      try {
        const { childId, latitude, longitude, parkId } = data;

        // Verify the child belongs to the user
        const child = await prisma.child.findFirst({
          where: {
            id: childId,
            parentId: socket.userId
          }
        });

        if (!child) {
          socket.emit('error', { message: 'Child not found or unauthorized' });
          return;
        }

        // Update child's current location
        await prisma.child.update({
          where: { id: childId },
          data: {
            currentLocation: parkId ? `Park: ${parkId}` : `${latitude}, ${longitude}`
          }
        });

        // Broadcast location update to nearby users (within park or area)
        if (parkId) {
          socket.to(`park_${parkId}`).emit('childLocationUpdate', {
            childId,
            childName: child.name,
            childAge: child.age,
            location: { latitude, longitude, parkId },
            timestamp: new Date()
          });
        }

        socket.emit('locationUpdateConfirmed', { childId, timestamp: new Date() });
      } catch (error) {
        console.error('Location update error:', error);
        socket.emit('error', { message: 'Failed to update location' });
      }
    });

    // Handle joining park rooms for real-time park activity
    socket.on('joinPark', async (data) => {
      try {
        const { parkId, childId } = data;

        // Verify the child belongs to the user
        const child = await prisma.child.findFirst({
          where: {
            id: childId,
            parentId: socket.userId
          }
        });

        if (!child) {
          socket.emit('error', { message: 'Child not found or unauthorized' });
          return;
        }

        // Join park room
        socket.join(`park_${parkId}`);

        // Notify others in the park
        socket.to(`park_${parkId}`).emit('childJoinedPark', {
          child: {
            id: child.id,
            name: child.name,
            age: child.age,
            avatar: child.avatar,
            interests: child.interests
          },
          timestamp: new Date()
        });

        // Send current park activity to the joining user
        const currentActivity = await prisma.playSession.findMany({
          where: {
            parkId,
            endTime: null
          },
          include: {
            child: {
              select: {
                id: true,
                name: true,
                age: true,
                avatar: true,
                interests: true
              }
            },
            game: true
          }
        });

        socket.emit('parkActivityUpdate', {
          parkId,
          activeChildren: currentActivity.map(session => session.child),
          activeSessions: currentActivity
        });

      } catch (error) {
        console.error('Join park error:', error);
        socket.emit('error', { message: 'Failed to join park' });
      }
    });

    // Handle leaving park
    socket.on('leavePark', async (data) => {
      try {
        const { parkId, childId } = data;

        // Verify the child belongs to the user
        const child = await prisma.child.findFirst({
          where: {
            id: childId,
            parentId: socket.userId
          }
        });

        if (!child) {
          socket.emit('error', { message: 'Child not found or unauthorized' });
          return;
        }

        // Leave park room
        socket.leave(`park_${parkId}`);

        // Notify others in the park
        socket.to(`park_${parkId}`).emit('childLeftPark', {
          childId: child.id,
          childName: child.name,
          timestamp: new Date()
        });

        socket.emit('leftParkConfirmed', { parkId, childId });

      } catch (error) {
        console.error('Leave park error:', error);
        socket.emit('error', { message: 'Failed to leave park' });
      }
    });

    // Handle play session start
    socket.on('startPlaySession', async (data) => {
      try {
        const { childId, parkId, gameId } = data;

        // Verify the child belongs to the user
        const child = await prisma.child.findFirst({
          where: {
            id: childId,
            parentId: socket.userId
          }
        });

        if (!child) {
          socket.emit('error', { message: 'Child not found or unauthorized' });
          return;
        }

        // Create play session
        const playSession = await prisma.playSession.create({
          data: {
            childId,
            parkId,
            gameId,
            startTime: new Date()
          },
          include: {
            child: {
              select: {
                id: true,
                name: true,
                age: true,
                avatar: true
              }
            },
            park: true,
            game: true
          }
        });

        // Update child status
        await prisma.child.update({
          where: { id: childId },
          data: { status: 'PLAYING' }
        });

        // Notify park members
        socket.to(`park_${parkId}`).emit('playSessionStarted', {
          session: playSession,
          timestamp: new Date()
        });

        socket.emit('playSessionStartConfirmed', { session: playSession });

      } catch (error) {
        console.error('Start play session error:', error);
        socket.emit('error', { message: 'Failed to start play session' });
      }
    });

    // Handle play session end
    socket.on('endPlaySession', async (data) => {
      try {
        const { sessionId } = data;

        // Find and verify the session
        const session = await prisma.playSession.findFirst({
          where: {
            id: sessionId,
            child: {
              parentId: socket.userId
            },
            endTime: null
          },
          include: {
            child: true,
            park: true,
            game: true
          }
        });

        if (!session) {
          socket.emit('error', { message: 'Play session not found or unauthorized' });
          return;
        }

        const endTime = new Date();
        const duration = Math.round((endTime.getTime() - session.startTime.getTime()) / (1000 * 60)); // minutes

        // Update session
        const updatedSession = await prisma.playSession.update({
          where: { id: sessionId },
          data: {
            endTime,
            duration
          },
          include: {
            child: true,
            park: true,
            game: true
          }
        });

        // Update child status back to available
        await prisma.child.update({
          where: { id: session.childId },
          data: { status: 'AVAILABLE' }
        });

        // Notify park members
        socket.to(`park_${session.parkId}`).emit('playSessionEnded', {
          session: updatedSession,
          duration,
          timestamp: endTime
        });

        socket.emit('playSessionEndConfirmed', { 
          session: updatedSession,
          duration 
        });

        // Check for badge progress updates
        // This could be expanded to check various badge criteria
        // For now, just update activity-related badges
        await updateBadgeProgress(session.childId, 'ACTIVITY', 1);

      } catch (error) {
        console.error('End play session error:', error);
        socket.emit('error', { message: 'Failed to end play session' });
      }
    });

    // Handle real-time messaging (for play requests, etc.)
    socket.on('sendMessage', async (data) => {
      try {
        const { recipientId, message, type = 'general' } = data;

        // Verify recipient exists
        const recipient = await prisma.user.findUnique({
          where: { id: recipientId },
          select: { id: true, firstName: true, lastName: true }
        });

        if (!recipient) {
          socket.emit('error', { message: 'Recipient not found' });
          return;
        }

        const messageData = {
          from: socket.user,
          message,
          type,
          timestamp: new Date()
        };

        // Send to recipient
        socket.to(`user_${recipientId}`).emit('newMessage', messageData);

        // Confirm to sender
        socket.emit('messageSent', { recipientId, timestamp: new Date() });

      } catch (error) {
        console.error('Send message error:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User ${socket.user?.firstName} disconnected: ${socket.id}`);
    });
  });
};

// Helper function to update badge progress
async function updateBadgeProgress(childId: string, category: string, increment: number = 1) {
  try {
    const badges = await prisma.badge.findMany({
      where: { category: category as any }
    });

    for (const badge of badges) {
      const userBadge = await prisma.userBadge.findUnique({
        where: {
          childId_badgeId: {
            childId,
            badgeId: badge.id
          }
        }
      });

      if (userBadge && !userBadge.earned) {
        const newProgress = Math.min(userBadge.progress + increment, userBadge.total);
        const isEarned = newProgress >= userBadge.total;

        await prisma.userBadge.update({
          where: {
            childId_badgeId: {
              childId,
              badgeId: badge.id
            }
          },
          data: {
            progress: newProgress,
            earned: isEarned,
            earnedAt: isEarned ? new Date() : null
          }
        });

        // If badge was earned, notify the parent
        if (isEarned) {
          const child = await prisma.child.findUnique({
            where: { id: childId },
            include: { parent: true }
          });

          if (child) {
            // This would emit to the parent's socket if they're connected
            // io.to(`user_${child.parentId}`).emit('badgeEarned', { child, badge });
          }
        }
      }
    }
  } catch (error) {
    console.error('Badge progress update error:', error);
  }
}
