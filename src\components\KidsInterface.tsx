
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUp, MapPin, Users, Gamepad, Star } from "lucide-react";
import PlaymatesFinder from "./PlaymatesFinder";
import GameSuggestions from "./GameSuggestions";
import BadgeCollection from "./BadgeCollection";

interface KidsInterfaceProps {
  onBack: () => void;
}

const KidsInterface = ({ onBack }: KidsInterfaceProps) => {
  const [activeTab, setActiveTab] = useState<'play' | 'games' | 'badges'>('play');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'games':
        return <GameSuggestions />;
      case 'badges':
        return <BadgeCollection />;
      default:
        return <PlaymatesFinder />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button onClick={onBack} variant="ghost" size="sm">
                <ArrowUp className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-blue-600">🎮 Kids Play Zone</h1>
                <p className="text-gray-600">Find friends and have fun outdoors!</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Welcome back,</div>
              <div className="font-semibold text-blue-600">Emma! 👧</div>
            </div>
          </div>
        </div>
      </div>

      {/* Fun Navigation */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex space-x-4">
            {[
              { id: 'play', label: 'Find Friends', icon: '👫', color: 'blue' },
              { id: 'games', label: 'Fun Games', icon: '🎯', color: 'green' },
              { id: 'badges', label: 'My Badges', icon: '🏆', color: 'purple' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-6 rounded-t-lg font-medium text-lg transition-colors ${
                  activeTab === tab.id
                    ? `bg-${tab.color}-100 text-${tab.color}-600 border-b-4 border-${tab.color}-500`
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <span className="text-2xl mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default KidsInterface;
