# Backend Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Build the application
FROM base AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 outplay

# Copy built application
COPY --from=builder --chown=outplay:nodejs /app/dist ./dist
COPY --from=builder --chown=outplay:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=outplay:nodejs /app/package.json ./package.json
COPY --from=builder --chown=outplay:nodejs /app/prisma ./prisma

# Create uploads directory
RUN mkdir -p uploads && chown outplay:nodejs uploads

USER outplay

EXPOSE 3001

ENV NODE_ENV=production
ENV PORT=3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

CMD ["node", "dist/index.js"]
