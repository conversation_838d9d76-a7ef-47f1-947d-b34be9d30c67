import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { validate, createPlayRequestSchema, updatePlayRequestSchema } from '../validation/schemas.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';
import { io } from '../index.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all play requests for the authenticated user
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { status, type = 'all' } = req.query;

  let whereClause: any = {};

  if (type === 'sent') {
    whereClause.senderId = req.user!.id;
  } else if (type === 'received') {
    whereClause.receiverId = req.user!.id;
  } else {
    whereClause.OR = [
      { senderId: req.user!.id },
      { receiverId: req.user!.id }
    ];
  }

  if (status) {
    whereClause.status = status;
  }

  const playRequests = await prisma.playRequest.findMany({
    where: whereClause,
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true
        }
      },
      receiver: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true
        }
      },
      childSender: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true,
          interests: true
        }
      },
      childReceiver: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true,
          interests: true
        }
      },
      park: true
    },
    orderBy: { createdAt: 'desc' }
  });

  res.json({ playRequests });
}));

// Get a specific play request
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const playRequest = await prisma.playRequest.findFirst({
    where: {
      id,
      OR: [
        { senderId: req.user!.id },
        { receiverId: req.user!.id }
      ]
    },
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true
        }
      },
      receiver: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true
        }
      },
      childSender: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true,
          interests: true
        }
      },
      childReceiver: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true,
          interests: true
        }
      },
      park: true
    }
  });

  if (!playRequest) {
    throw createError('Play request not found', 404);
  }

  res.json({ playRequest });
}));

// Create a new play request
router.post('/', validate(createPlayRequestSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { childReceiverId, childSenderId, parkId, suggestedTime, duration, suggestedGame, message } = req.body;

  // Verify the sender child belongs to the authenticated user
  const senderChild = await prisma.child.findFirst({
    where: {
      id: childSenderId,
      parentId: req.user!.id
    }
  });

  if (!senderChild) {
    throw createError('Sender child not found or not authorized', 404);
  }

  // Get the receiver child and their parent
  const receiverChild = await prisma.child.findUnique({
    where: { id: childReceiverId },
    include: {
      parent: {
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      }
    }
  });

  if (!receiverChild) {
    throw createError('Receiver child not found', 404);
  }

  // Verify the park exists
  const park = await prisma.park.findUnique({
    where: { id: parkId }
  });

  if (!park) {
    throw createError('Park not found', 404);
  }

  // Create the play request
  const playRequest = await prisma.playRequest.create({
    data: {
      senderId: req.user!.id,
      receiverId: receiverChild.parentId,
      childSenderId,
      childReceiverId,
      parkId,
      suggestedTime: new Date(suggestedTime),
      duration,
      suggestedGame,
      message
    },
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      },
      childSender: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true
        }
      },
      childReceiver: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true
        }
      },
      park: true
    }
  });

  // Emit real-time notification to the receiver
  io.to(`user_${receiverChild.parentId}`).emit('newPlayRequest', {
    playRequest,
    message: `New play request from ${req.user!.firstName} for ${receiverChild.name}`
  });

  res.status(201).json({
    message: 'Play request sent successfully',
    playRequest
  });
}));

// Update play request status (approve/decline)
router.put('/:id', validate(updatePlayRequestSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { status } = req.body;

  // Verify the play request exists and user is the receiver
  const existingRequest = await prisma.playRequest.findFirst({
    where: {
      id,
      receiverId: req.user!.id,
      status: 'PENDING'
    },
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      },
      childSender: {
        select: {
          id: true,
          name: true,
          age: true
        }
      },
      childReceiver: {
        select: {
          id: true,
          name: true,
          age: true
        }
      }
    }
  });

  if (!existingRequest) {
    throw createError('Play request not found or already processed', 404);
  }

  // Update the request status
  const playRequest = await prisma.playRequest.update({
    where: { id },
    data: { status },
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      },
      receiver: {
        select: {
          id: true,
          firstName: true,
          lastName: true
        }
      },
      childSender: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true
        }
      },
      childReceiver: {
        select: {
          id: true,
          name: true,
          age: true,
          avatar: true
        }
      },
      park: true
    }
  });

  // If approved, create friendship if it doesn't exist
  if (status === 'APPROVED') {
    const existingFriendship = await prisma.friendship.findFirst({
      where: {
        OR: [
          { child1Id: existingRequest.childSenderId, child2Id: existingRequest.childReceiverId },
          { child1Id: existingRequest.childReceiverId, child2Id: existingRequest.childSenderId }
        ]
      }
    });

    if (!existingFriendship) {
      await prisma.friendship.create({
        data: {
          child1Id: existingRequest.childSenderId,
          child2Id: existingRequest.childReceiverId,
          status: 'ACCEPTED'
        }
      });
    }
  }

  // Emit real-time notification to the sender
  io.to(`user_${existingRequest.senderId}`).emit('playRequestUpdate', {
    playRequest,
    message: `Your play request for ${existingRequest.childSender.name} was ${status.toLowerCase()}`
  });

  res.json({
    message: `Play request ${status.toLowerCase()} successfully`,
    playRequest
  });
}));

// Delete a play request
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Verify the play request exists and user is the sender
  const existingRequest = await prisma.playRequest.findFirst({
    where: {
      id,
      senderId: req.user!.id
    }
  });

  if (!existingRequest) {
    throw createError('Play request not found or not authorized', 404);
  }

  await prisma.playRequest.delete({
    where: { id }
  });

  res.json({
    message: 'Play request deleted successfully'
  });
}));

export default router;
