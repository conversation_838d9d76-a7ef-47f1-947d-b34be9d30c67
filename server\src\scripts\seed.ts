import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create sample parks
  const parks = await Promise.all([
    prisma.park.create({
      data: {
        name: "Maple Park",
        address: "123 Maple Street, Springfield, IL",
        latitude: 39.7817,
        longitude: -89.6501,
        amenities: ["Playground", "Soccer Field", "Picnic Area", "Restrooms", "Parking"],
        safetyRating: 5,
        description: "A beautiful family-friendly park with modern playground equipment and well-maintained facilities."
      }
    }),
    prisma.park.create({
      data: {
        name: "Sunny Playground",
        address: "456 Oak Avenue, Springfield, IL",
        latitude: 39.7900,
        longitude: -89.6400,
        amenities: ["Playground", "Sandbox", "Swings", "Benches"],
        safetyRating: 4,
        description: "A cozy neighborhood playground perfect for younger children."
      }
    }),
    prisma.park.create({
      data: {
        name: "Central Park",
        address: "789 Central Boulevard, Springfield, IL",
        latitude: 39.7750,
        longitude: -89.6600,
        amenities: ["Basketball Court", "Tennis Court", "Walking Trail", "Playground", "Picnic Tables"],
        safetyRating: 5,
        description: "Large community park with sports facilities and recreational areas for all ages."
      }
    })
  ]);

  console.log('✅ Created sample parks');

  // Create sample games
  const games = await Promise.all([
    prisma.game.create({
      data: {
        name: "Tag",
        emoji: "🏃‍♀️",
        description: "Classic chase game where one person is 'it' and tries to tag others!",
        instructions: [
          "Choose who is 'it' first",
          "Set boundaries for the play area",
          "The person who is 'it' counts to 10",
          "Run and try not to get tagged!",
          "When tagged, you become 'it'"
        ],
        minPlayers: 3,
        maxPlayers: 10,
        minAge: 4,
        maxAge: 16,
        difficulty: "EASY",
        equipment: [],
        duration: 20,
        popularity: 95
      }
    }),
    prisma.game.create({
      data: {
        name: "Hide and Seek",
        emoji: "🙈",
        description: "One person counts while others hide, then tries to find everyone!",
        instructions: [
          "Choose one person to be the seeker",
          "Seeker closes eyes and counts to 30",
          "Everyone else finds hiding spots",
          "Seeker tries to find all hidden players",
          "First person found becomes the next seeker"
        ],
        minPlayers: 3,
        maxPlayers: 8,
        minAge: 4,
        maxAge: 14,
        difficulty: "EASY",
        equipment: [],
        duration: 25,
        popularity: 88
      }
    }),
    prisma.game.create({
      data: {
        name: "Red Light, Green Light",
        emoji: "🚦",
        description: "Move when it's green light, freeze when it's red light!",
        instructions: [
          "One person is the traffic light",
          "Others line up at the starting line",
          "Green light means run toward the traffic light",
          "Red light means freeze completely",
          "First to reach the traffic light wins"
        ],
        minPlayers: 3,
        maxPlayers: 12,
        minAge: 3,
        maxAge: 12,
        difficulty: "EASY",
        equipment: [],
        duration: 15,
        popularity: 82
      }
    }),
    prisma.game.create({
      data: {
        name: "Capture the Flag",
        emoji: "🏴",
        description: "Team game where you try to capture the other team's flag!",
        instructions: [
          "Divide into two teams",
          "Each team has a flag in their territory",
          "Try to capture the other team's flag",
          "If tagged in enemy territory, go to jail",
          "Teammates can free you from jail"
        ],
        minPlayers: 6,
        maxPlayers: 20,
        minAge: 7,
        maxAge: 16,
        difficulty: "MEDIUM",
        equipment: ["2 flags or bandanas"],
        duration: 30,
        popularity: 75
      }
    }),
    prisma.game.create({
      data: {
        name: "Duck Duck Goose",
        emoji: "🦆",
        description: "Sit in a circle and wait to be chosen as the goose!",
        instructions: [
          "Sit in a circle facing inward",
          "One person walks around tapping heads",
          "Say 'duck' for each tap",
          "When you say 'goose', that person chases you",
          "Try to sit in their spot before being tagged"
        ],
        minPlayers: 5,
        maxPlayers: 15,
        minAge: 4,
        maxAge: 12,
        difficulty: "EASY",
        equipment: [],
        duration: 20,
        popularity: 70
      }
    })
  ]);

  console.log('✅ Created sample games');

  // Create sample badges
  const badges = await Promise.all([
    prisma.badge.create({
      data: {
        name: "First Playdate",
        emoji: "🤝",
        description: "Made your first friend on OutPlay!",
        category: "FRIENDSHIP",
        rarity: "COMMON",
        criteria: { total: 1, type: "playdate_count" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Park Explorer",
        emoji: "🌳",
        description: "Visited 3 different parks",
        category: "ADVENTURE",
        rarity: "COMMON",
        criteria: { total: 3, type: "unique_parks" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Game Master",
        emoji: "🎯",
        description: "Played 5 different games",
        category: "GAMES",
        rarity: "RARE",
        criteria: { total: 5, type: "unique_games" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Sunshine Player",
        emoji: "☀️",
        description: "Played outside for 10 hours this month",
        category: "ACTIVITY",
        rarity: "EPIC",
        criteria: { total: 600, type: "monthly_minutes" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Social Butterfly",
        emoji: "🦋",
        description: "Play with 10 different friends",
        category: "FRIENDSHIP",
        rarity: "RARE",
        criteria: { total: 10, type: "unique_friends" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Weather Warrior",
        emoji: "🌦️",
        description: "Play in 3 different weather conditions",
        category: "ADVENTURE",
        rarity: "EPIC",
        criteria: { total: 3, type: "weather_conditions" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Team Captain",
        emoji: "👑",
        description: "Lead 5 group games",
        category: "LEADERSHIP",
        rarity: "LEGENDARY",
        criteria: { total: 5, type: "games_led" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Daily Player",
        emoji: "📅",
        description: "Play outside 7 days in a row",
        category: "ACTIVITY",
        rarity: "RARE",
        criteria: { total: 7, type: "consecutive_days" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Safety First",
        emoji: "🛡️",
        description: "Complete safety training",
        category: "SAFETY",
        rarity: "COMMON",
        criteria: { total: 1, type: "safety_training" }
      }
    }),
    prisma.badge.create({
      data: {
        name: "Marathon Player",
        emoji: "🏃‍♂️",
        description: "Play for 3 hours in a single day",
        category: "ACTIVITY",
        rarity: "EPIC",
        criteria: { total: 180, type: "daily_minutes" }
      }
    })
  ]);

  console.log('✅ Created sample badges');

  // Create a demo user
  const hashedPassword = await bcrypt.hash('demo123', 12);
  const demoUser = await prisma.user.create({
    data: {
      email: "<EMAIL>",
      password: hashedPassword,
      firstName: "Demo",
      lastName: "Parent",
      phone: "******-0123"
    }
  });

  console.log('✅ Created demo user (email: <EMAIL>, password: demo123)');

  // Create demo children
  const demoChildren = await Promise.all([
    prisma.child.create({
      data: {
        name: "Emma",
        age: 8,
        interests: ["Tag", "Soccer", "Drawing", "Hide and Seek"],
        parentId: demoUser.id,
        status: "AVAILABLE"
      }
    }),
    prisma.child.create({
      data: {
        name: "Jake",
        age: 6,
        interests: ["Playground", "Bikes", "Sandbox", "Swings"],
        parentId: demoUser.id,
        status: "AVAILABLE"
      }
    })
  ]);

  console.log('✅ Created demo children');

  // Initialize badges for demo children
  for (const child of demoChildren) {
    const userBadges = badges.map(badge => ({
      childId: child.id,
      badgeId: badge.id,
      total: (badge.criteria as any).total || 1,
      progress: 0
    }));

    await prisma.userBadge.createMany({
      data: userBadges
    });

    // Give some progress to make it interesting
    await prisma.userBadge.update({
      where: {
        childId_badgeId: {
          childId: child.id,
          badgeId: badges[0].id // First Playdate
        }
      },
      data: {
        progress: 1,
        earned: true,
        earnedAt: new Date()
      }
    });
  }

  console.log('✅ Initialized badges for demo children');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📝 Demo Account:');
  console.log('Email: <EMAIL>');
  console.log('Password: demo123');
  console.log('\n🏞️ Sample parks, games, and badges have been created.');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
