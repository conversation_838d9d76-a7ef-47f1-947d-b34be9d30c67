# Database
DATABASE_URL="postgresql://username:password@localhost:5432/outplay_db"

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Origins (comma-separated)
CORS_ORIGINS="http://localhost:5173,http://localhost:3000"

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH="./uploads"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Socket.io
SOCKET_CORS_ORIGINS="http://localhost:5173,http://localhost:3000"
