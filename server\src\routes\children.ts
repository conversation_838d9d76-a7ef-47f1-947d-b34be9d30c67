import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { validate, createChildSchema, updateChildSchema } from '../validation/schemas.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all children for the authenticated user
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const children = await prisma.child.findMany({
    where: { parentId: req.user!.id },
    include: {
      badges: {
        include: {
          badge: true
        }
      },
      playSessions: {
        include: {
          park: true,
          game: true
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      }
    }
  });

  res.json({ children });
}));

// Get a specific child
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const child = await prisma.child.findFirst({
    where: { 
      id,
      parentId: req.user!.id 
    },
    include: {
      badges: {
        include: {
          badge: true
        }
      },
      playSessions: {
        include: {
          park: true,
          game: true
        },
        orderBy: { createdAt: 'desc' }
      },
      friendships1: {
        include: {
          child2: {
            select: {
              id: true,
              name: true,
              age: true,
              avatar: true,
              status: true
            }
          }
        }
      },
      friendships2: {
        include: {
          child1: {
            select: {
              id: true,
              name: true,
              age: true,
              avatar: true,
              status: true
            }
          }
        }
      }
    }
  });

  if (!child) {
    throw createError('Child not found', 404);
  }

  // Combine friendships from both directions
  const friends = [
    ...child.friendships1.map(f => f.child2),
    ...child.friendships2.map(f => f.child1)
  ];

  res.json({ 
    child: {
      ...child,
      friends
    }
  });
}));

// Create a new child profile
router.post('/', validate(createChildSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { name, age, interests, avatar } = req.body;

  const child = await prisma.child.create({
    data: {
      name,
      age,
      interests,
      avatar,
      parentId: req.user!.id
    },
    include: {
      badges: {
        include: {
          badge: true
        }
      }
    }
  });

  // Initialize badges for the new child
  const allBadges = await prisma.badge.findMany();
  
  const userBadges = allBadges.map(badge => ({
    childId: child.id,
    badgeId: badge.id,
    total: (badge.criteria as any).total || 1,
    progress: 0
  }));

  await prisma.userBadge.createMany({
    data: userBadges
  });

  res.status(201).json({
    message: 'Child profile created successfully',
    child
  });
}));

// Update a child profile
router.put('/:id', validate(updateChildSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Verify the child belongs to the authenticated user
  const existingChild = await prisma.child.findFirst({
    where: { 
      id,
      parentId: req.user!.id 
    }
  });

  if (!existingChild) {
    throw createError('Child not found', 404);
  }

  const child = await prisma.child.update({
    where: { id },
    data: updateData,
    include: {
      badges: {
        include: {
          badge: true
        }
      }
    }
  });

  res.json({
    message: 'Child profile updated successfully',
    child
  });
}));

// Delete a child profile
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Verify the child belongs to the authenticated user
  const existingChild = await prisma.child.findFirst({
    where: { 
      id,
      parentId: req.user!.id 
    }
  });

  if (!existingChild) {
    throw createError('Child not found', 404);
  }

  await prisma.child.delete({
    where: { id }
  });

  res.json({
    message: 'Child profile deleted successfully'
  });
}));

// Get nearby children for playmate finding
router.get('/:id/nearby', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { latitude, longitude, radius = 5 } = req.query;

  // Verify the child belongs to the authenticated user
  const child = await prisma.child.findFirst({
    where: { 
      id,
      parentId: req.user!.id 
    }
  });

  if (!child) {
    throw createError('Child not found', 404);
  }

  if (!latitude || !longitude) {
    throw createError('Location coordinates required', 400);
  }

  // For now, return mock data since we need geolocation calculations
  // In production, you'd use PostGIS or similar for proper geo queries
  const nearbyChildren = await prisma.child.findMany({
    where: {
      NOT: { id },
      status: 'AVAILABLE',
      age: {
        gte: Math.max(child.age - 2, 3),
        lte: Math.min(child.age + 2, 17)
      }
    },
    include: {
      parent: {
        select: {
          firstName: true,
          lastName: true
        }
      }
    },
    take: 10
  });

  res.json({ nearbyChildren });
}));

export default router;
