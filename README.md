# 🌳 OutPlay - Kids Outdoor Playtime App

**Connect neighboring kids for outdoor fun and safe playtime activities!**

OutPlay is a full-stack Progressive Web App (PWA) that helps parents connect their children with nearby playmates for supervised outdoor activities. Built with modern technologies, it works seamlessly as both a web application and mobile app.

## ✨ Features

### 👨‍👩‍👧‍👦 For Parents

- **Child Profile Management** - Create and manage multiple child profiles with ages, interests, and photos
- **Play Request System** - Approve/decline play requests from other parents
- **Real-time Park Activity** - See which parks have kids playing right now
- **Safety Controls** - Geo-fenced safe zones and trusted contact networks
- **Activity Tracking** - Monitor outdoor time and reduce screen time
- **Badge System** - Gamified incentives for outdoor play

### 🧒 For Kids

- **Playmate Finder** - Discover nearby kids ready to play
- **Game Suggestions** - Browse age-appropriate outdoor games with instructions
- **Badge Collection** - Earn badges for outdoor activities and achievements
- **Park Check-in** - See who's at nearby parks in real-time
- **Friend System** - Build connections with other children

### 🏞️ Core Functionality

- **Geolocation-based Matching** - Find playmates and parks within walking distance
- **Real-time Updates** - Live park activity and play session tracking
- **Weather Integration** - Park conditions and weather alerts
- **Offline Support** - PWA capabilities for offline functionality
- **Cross-platform** - Works on web, iOS, and Android

## 🛠️ Technology Stack

### Frontend

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **shadcn/ui** for modern UI components
- **TanStack Query** for data fetching and caching
- **Socket.io Client** for real-time features
- **PWA** with service worker for offline support

### Backend

- **Node.js** with Express and TypeScript
- **PostgreSQL** database with Prisma ORM
- **Socket.io** for real-time communication
- **JWT** authentication with bcrypt
- **Multer** for file uploads
- **Joi** for request validation
- **Rate limiting** and security middleware

### Infrastructure

- **Docker** support for easy deployment
- **PWA** manifest and service worker
- **Real-time** location sharing and notifications
- **File upload** system for avatars and images

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- npm or yarn

### 1. Clone the Repository

```bash
git clone <repository-url>
cd park-pal-playtime
```

### 2. Install Dependencies

```bash
# Install frontend dependencies
npm install

# Install backend dependencies
npm run server:install
```

### 3. Database Setup

```bash
# Set up your PostgreSQL database
# Update the DATABASE_URL in server/.env

# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Seed with sample data
cd server && npm run db:seed
```

### 4. Environment Configuration

```bash
# Frontend (.env)
VITE_API_URL=http://localhost:3001/api
VITE_SOCKET_URL=http://localhost:3001

# Backend (server/.env)
DATABASE_URL="postgresql://username:password@localhost:5432/outplay_db"
JWT_SECRET="your-super-secret-jwt-key"
PORT=3001
```

### 5. Start Development Servers

```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:client  # Frontend on http://localhost:5173
npm run dev:server  # Backend on http://localhost:3001
```

## 📱 PWA Installation

OutPlay works as a Progressive Web App and can be installed on mobile devices:

1. **iOS Safari**: Tap Share → Add to Home Screen
2. **Android Chrome**: Tap Menu → Add to Home Screen
3. **Desktop**: Look for the install prompt in the address bar

## 🎮 Demo Account

Try the app with the pre-seeded demo account:

- **Email**: <EMAIL>
- **Password**: demo123

The demo includes:

- 2 sample children (Emma, 8 and Jake, 6)
- 3 nearby parks with sample data
- Various games and badges to explore

## 📊 Database Schema

### Core Models

- **Users** - Parent accounts with authentication
- **Children** - Child profiles linked to parents
- **Parks** - Location data with amenities and safety ratings
- **PlayRequests** - Friend and play requests between children
- **Games** - Outdoor games with rules and age recommendations
- **Badges** - Achievement system with progress tracking
- **PlaySessions** - Activity tracking and time logging

## 🔧 API Endpoints

### Authentication

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user profile

### Children Management

- `GET /api/children` - Get user's children
- `POST /api/children` - Create child profile
- `PUT /api/children/:id` - Update child profile
- `GET /api/children/:id/nearby` - Find nearby children

### Parks & Location

- `GET /api/parks` - Get all parks
- `POST /api/parks/nearby` - Find parks by location
- `GET /api/parks/:id/activity` - Get park activity history

### Play Requests

- `GET /api/play-requests` - Get play requests
- `POST /api/play-requests` - Send play request
- `PUT /api/play-requests/:id` - Approve/decline request

### Real-time Events (Socket.io)

- `updateLocation` - Share child location
- `joinPark` - Join park activity room
- `startPlaySession` - Begin play session
- `newPlayRequest` - Real-time play request notifications

## 🔒 Security Features

- **JWT Authentication** with secure token storage
- **Rate Limiting** to prevent abuse
- **Input Validation** with Joi schemas
- **CORS Protection** for cross-origin requests
- **Helmet.js** for security headers
- **Password Hashing** with bcrypt
- **File Upload Validation** for images only

## 🌐 Deployment

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose up --build
```

### Manual Deployment

1. Build the frontend: `npm run build`
2. Set up PostgreSQL database
3. Configure environment variables
4. Start the backend: `cd server && npm start`
5. Serve frontend build files

## 🧪 Testing

```bash
# Run frontend tests
npm test

# Run backend tests
cd server && npm test

# Run E2E tests
npm run test:e2e
```

## 📈 Performance Features

- **Service Worker** caching for offline support
- **Image Optimization** with lazy loading
- **API Response Caching** with TanStack Query
- **Real-time Updates** without polling
- **Optimistic UI Updates** for better UX
- **Background Sync** for offline actions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join community discussions
- **Email**: <EMAIL>

## 🎯 Roadmap

- [ ] **Mobile Apps** - Native iOS and Android apps
- [ ] **Video Calls** - In-app video chat for remote play
- [ ] **AI Matching** - Smart playmate recommendations
- [ ] **Event Planning** - Organize larger group activities
- [ ] **Parent Chat** - Direct messaging between parents
- [ ] **Weather Integration** - Real-time weather data
- [ ] **Accessibility** - Enhanced accessibility features
- [ ] **Multi-language** - Internationalization support

---

**Made with ❤️ for kids who love to play outside!**
