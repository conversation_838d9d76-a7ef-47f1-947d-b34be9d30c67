import Joi from 'joi';

// Auth schemas
export const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional()
});

export const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

// Child schemas
export const createChildSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  age: Joi.number().integer().min(3).max(17).required(),
  interests: Joi.array().items(Joi.string().max(50)).max(10).required(),
  avatar: Joi.string().uri().optional()
});

export const updateChildSchema = Joi.object({
  name: Joi.string().min(2).max(50).optional(),
  age: Joi.number().integer().min(3).max(17).optional(),
  interests: Joi.array().items(Joi.string().max(50)).max(10).optional(),
  avatar: Joi.string().uri().optional(),
  status: Joi.string().valid('AVAILABLE', 'PLAYING', 'OFFLINE').optional(),
  currentLocation: Joi.string().max(100).optional()
});

// Park schemas
export const createParkSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  address: Joi.string().min(5).max(200).required(),
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  amenities: Joi.array().items(Joi.string().max(50)).max(20).required(),
  safetyRating: Joi.number().integer().min(1).max(5).optional(),
  description: Joi.string().max(500).optional()
});

// Play request schemas
export const createPlayRequestSchema = Joi.object({
  childReceiverId: Joi.string().required(),
  childSenderId: Joi.string().required(),
  parkId: Joi.string().required(),
  suggestedTime: Joi.date().iso().min('now').required(),
  duration: Joi.number().integer().min(15).max(480).required(), // 15 minutes to 8 hours
  suggestedGame: Joi.string().max(100).optional(),
  message: Joi.string().max(500).optional()
});

export const updatePlayRequestSchema = Joi.object({
  status: Joi.string().valid('APPROVED', 'DECLINED').required()
});

// Game schemas
export const createGameSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  emoji: Joi.string().max(10).required(),
  description: Joi.string().min(10).max(500).required(),
  instructions: Joi.array().items(Joi.string().max(200)).min(1).max(20).required(),
  minPlayers: Joi.number().integer().min(1).max(50).required(),
  maxPlayers: Joi.number().integer().min(1).max(50).required(),
  minAge: Joi.number().integer().min(3).max(17).required(),
  maxAge: Joi.number().integer().min(3).max(17).required(),
  difficulty: Joi.string().valid('EASY', 'MEDIUM', 'HARD').required(),
  equipment: Joi.array().items(Joi.string().max(50)).max(20).required(),
  duration: Joi.number().integer().min(5).max(240).required() // 5 minutes to 4 hours
});

// User update schema
export const updateUserSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).optional(),
  lastName: Joi.string().min(2).max(50).optional(),
  phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional(),
  avatar: Joi.string().uri().optional()
});

// Location schema
export const locationSchema = Joi.object({
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  radius: Joi.number().min(0.1).max(50).optional().default(5) // Default 5km radius
});

// Play session schema
export const createPlaySessionSchema = Joi.object({
  childId: Joi.string().required(),
  parkId: Joi.string().required(),
  gameId: Joi.string().optional(),
  startTime: Joi.date().iso().required(),
  endTime: Joi.date().iso().optional(),
  notes: Joi.string().max(500).optional()
});

// Validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => detail.message)
      });
    }
    next();
  };
};
