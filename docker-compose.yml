version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: outplay-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: outplay_db
      POSTGRES_USER: outplay_user
      POSTGRES_PASSWORD: outplay_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - outplay-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U outplay_user -d outplay_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: outplay-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************************/outplay_db
      JWT_SECRET: ${JWT_SECRET:-outplay-super-secret-jwt-key-change-in-production}
      PORT: 3001
      CORS_ORIGINS: http://localhost:3000,http://localhost:5173
      SOCKET_CORS_ORIGINS: http://localhost:3000,http://localhost:5173
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - outplay-network
    volumes:
      - uploads_data:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (for production)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      args:
        VITE_API_URL: http://localhost:3001/api
        VITE_SOCKET_URL: http://localhost:3001
    container_name: outplay-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - outplay-network

  # Redis for session storage and caching (optional)
  redis:
    image: redis:7-alpine
    container_name: outplay-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - outplay-network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  postgres_data:
    driver: local
  uploads_data:
    driver: local
  redis_data:
    driver: local

networks:
  outplay-network:
    driver: bridge
