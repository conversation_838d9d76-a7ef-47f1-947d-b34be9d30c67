import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Plus, Users, Clock, MapPin } from "lucide-react";

const ChildProfileManager = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [children] = useState([
    {
      id: 1,
      name: "<PERSON>",
      age: 8,
      interests: ["Tag", "Soccer", "Drawing"],
      status: "Available",
      currentLocation: "Home",
      todayActivity: "45 minutes"
    },
    {
      id: 2,
      name: "<PERSON>",
      age: 6,
      interests: ["Hi<PERSON> and Seek", "Playground", "Bikes"],
      status: "Playing at Maple Park",
      currentLocation: "Maple Park",
      todayActivity: "30 minutes"
    }
  ]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Child Profiles</h2>
          <p className="text-gray-600">Manage your children's profiles and preferences</p>
        </div>
        <Button 
          onClick={() => setShowAddForm(!showAddForm)}
          className="bg-green-600 hover:bg-green-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Child
        </Button>
      </div>

      {/* Add Child Form */}
      {showAddForm && (
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle>Add New Child Profile</CardTitle>
            <CardDescription>Create a profile for your child to start connecting with playmates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="childName">Child's Name</Label>
                <Input id="childName" placeholder="Enter name" />
              </div>
              <div>
                <Label htmlFor="childAge">Age</Label>
                <Input id="childAge" type="number" placeholder="Enter age" />
              </div>
              <div className="md:col-span-2">
                <Label htmlFor="interests">Interests (comma separated)</Label>
                <Input id="interests" placeholder="e.g., Soccer, Tag, Drawing, Bikes" />
              </div>
            </div>
            <div className="flex gap-4 mt-6">
              <Button className="bg-green-600 hover:bg-green-700">Create Profile</Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>Cancel</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Children */}
      <div className="grid md:grid-cols-2 gap-6">
        {children.map((child) => (
          <Card key={child.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{child.name}</CardTitle>
                  <CardDescription>Age {child.age}</CardDescription>
                </div>
                <Badge 
                  className={
                    child.status === "Available" 
                      ? "bg-green-100 text-green-800" 
                      : "bg-blue-100 text-blue-800"
                  }
                >
                  {child.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Interests */}
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-2">Interests</div>
                  <div className="flex flex-wrap gap-2">
                    {child.interests.map((interest, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {interest}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{child.currentLocation}</span>
                </div>

                {/* Today's Activity */}
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>Today: {child.todayActivity} outdoor time</span>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-4">
                  <Button size="sm" variant="outline">Edit Profile</Button>
                  <Button size="sm" variant="outline">View Activity</Button>
                  {child.status === "Available" && (
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      Set as Playing
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ChildProfileManager;
