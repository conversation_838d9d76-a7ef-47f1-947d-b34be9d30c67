{"name": "outplay-server", "version": "1.0.0", "description": "Backend server for OutPlay - Kids outdoor playtime app", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "joi": "^17.11.0", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "prisma": "^5.7.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}}