import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { validate, createGameSchema } from '../validation/schemas.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all games
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { difficulty, minAge, maxAge, minPlayers, maxPlayers } = req.query;

  let whereClause: any = {};

  if (difficulty) {
    whereClause.difficulty = difficulty;
  }

  if (minAge) {
    whereClause.minAge = { gte: Number(minAge) };
  }

  if (maxAge) {
    whereClause.maxAge = { lte: Number(maxAge) };
  }

  if (minPlayers) {
    whereClause.minPlayers = { gte: Number(minPlayers) };
  }

  if (maxPlayers) {
    whereClause.maxPlayers = { lte: Number(maxPlayers) };
  }

  const games = await prisma.game.findMany({
    where: whereClause,
    include: {
      playSessions: {
        select: {
          id: true,
          startTime: true
        }
      }
    },
    orderBy: { popularity: 'desc' }
  });

  // Add play count to each game
  const gamesWithStats = games.map(game => ({
    ...game,
    playCount: game.playSessions.length,
    playSessions: undefined // Remove the sessions from response
  }));

  res.json({ games: gamesWithStats });
}));

// Get a specific game
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const game = await prisma.game.findUnique({
    where: { id },
    include: {
      playSessions: {
        include: {
          child: {
            select: {
              id: true,
              name: true,
              age: true,
              avatar: true
            }
          },
          park: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: { startTime: 'desc' },
        take: 10
      }
    }
  });

  if (!game) {
    throw createError('Game not found', 404);
  }

  res.json({
    game: {
      ...game,
      playCount: game.playSessions.length,
      recentSessions: game.playSessions
    }
  });
}));

// Create a new game
router.post('/', validate(createGameSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const gameData = req.body;

  // Validate that maxPlayers >= minPlayers and maxAge >= minAge
  if (gameData.maxPlayers < gameData.minPlayers) {
    throw createError('Maximum players must be greater than or equal to minimum players', 400);
  }

  if (gameData.maxAge < gameData.minAge) {
    throw createError('Maximum age must be greater than or equal to minimum age', 400);
  }

  const game = await prisma.game.create({
    data: gameData
  });

  res.status(201).json({
    message: 'Game created successfully',
    game
  });
}));

// Update a game
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Validate constraints if they're being updated
  if (updateData.maxPlayers && updateData.minPlayers && updateData.maxPlayers < updateData.minPlayers) {
    throw createError('Maximum players must be greater than or equal to minimum players', 400);
  }

  if (updateData.maxAge && updateData.minAge && updateData.maxAge < updateData.minAge) {
    throw createError('Maximum age must be greater than or equal to minimum age', 400);
  }

  const game = await prisma.game.update({
    where: { id },
    data: updateData
  });

  res.json({
    message: 'Game updated successfully',
    game
  });
}));

// Delete a game
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if game has any play sessions
  const sessionsCount = await prisma.playSession.count({
    where: { gameId: id }
  });

  if (sessionsCount > 0) {
    throw createError('Cannot delete game that has been played. Consider marking it as inactive instead.', 400);
  }

  await prisma.game.delete({
    where: { id }
  });

  res.json({
    message: 'Game deleted successfully'
  });
}));

// Get games suitable for specific children
router.post('/suggestions', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { childIds, playerCount } = req.body;

  if (!childIds || !Array.isArray(childIds) || childIds.length === 0) {
    throw createError('Child IDs are required', 400);
  }

  // Get children details
  const children = await prisma.child.findMany({
    where: {
      id: { in: childIds },
      parent: { id: req.user!.id }
    }
  });

  if (children.length === 0) {
    throw createError('No valid children found', 404);
  }

  const minAge = Math.min(...children.map(c => c.age));
  const maxAge = Math.max(...children.map(c => c.age));
  const actualPlayerCount = playerCount || children.length;

  // Find suitable games
  const suitableGames = await prisma.game.findMany({
    where: {
      minAge: { lte: maxAge },
      maxAge: { gte: minAge },
      minPlayers: { lte: actualPlayerCount },
      maxPlayers: { gte: actualPlayerCount }
    },
    include: {
      playSessions: {
        select: {
          id: true
        }
      }
    },
    orderBy: { popularity: 'desc' }
  });

  // Add compatibility score and play count
  const gamesWithScore = suitableGames.map(game => {
    let score = 0;
    
    // Age compatibility (higher score for better age match)
    const ageRange = game.maxAge - game.minAge;
    const childrenAgeRange = maxAge - minAge;
    if (childrenAgeRange <= ageRange) score += 3;
    else score += 1;

    // Player count compatibility
    if (actualPlayerCount >= game.minPlayers && actualPlayerCount <= game.maxPlayers) {
      score += 2;
    }

    // Popularity bonus
    score += Math.min(game.popularity / 10, 2);

    return {
      ...game,
      compatibilityScore: score,
      playCount: game.playSessions.length,
      playSessions: undefined
    };
  }).sort((a, b) => b.compatibilityScore - a.compatibilityScore);

  res.json({
    games: gamesWithScore,
    childrenAgeRange: { min: minAge, max: maxAge },
    playerCount: actualPlayerCount
  });
}));

// Increment game popularity when played
router.post('/:id/played', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const game = await prisma.game.update({
    where: { id },
    data: {
      popularity: { increment: 1 }
    }
  });

  res.json({
    message: 'Game popularity updated',
    game
  });
}));

export default router;
