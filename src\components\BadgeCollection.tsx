
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Clock, Users, MapPin } from "lucide-react";

const BadgeCollection = () => {
  const [earnedBadges] = useState([
    {
      id: 1,
      name: "First Playdate",
      emoji: "🤝",
      description: "Made your first friend on OutPlay!",
      dateEarned: "2 days ago",
      category: "Friendship",
      rarity: "Common"
    },
    {
      id: 2,
      name: "Park Explorer",
      emoji: "🌳",
      description: "Visited 3 different parks",
      dateEarned: "1 week ago",
      category: "Adventure",
      rarity: "Common"
    },
    {
      id: 3,
      name: "Game Master",
      emoji: "🎯",
      description: "Played 5 different games",
      dateEarned: "3 days ago",
      category: "Games",
      rarity: "Rare"
    },
    {
      id: 4,
      name: "Sunshine Player",
      emoji: "☀️",
      description: "Played outside for 10 hours this month",
      dateEarned: "Yesterday",
      category: "Activity",
      rarity: "Epic"
    }
  ]);

  const [availableBadges] = useState([
    {
      id: 5,
      name: "Social Butterfly",
      emoji: "🦋",
      description: "Play with 10 different friends",
      progress: 6,
      total: 10,
      category: "Friendship",
      rarity: "Rare"
    },
    {
      id: 6,
      name: "Weather Warrior",
      emoji: "🌦️",
      description: "Play in 3 different weather conditions",
      progress: 1,
      total: 3,
      category: "Adventure",
      rarity: "Epic"
    },
    {
      id: 7,
      name: "Team Captain",
      emoji: "👑",
      description: "Lead 5 group games",
      progress: 2,
      total: 5,
      category: "Leadership",
      rarity: "Legendary"
    },
    {
      id: 8,
      name: "Daily Player",
      emoji: "📅",
      description: "Play outside 7 days in a row",
      progress: 4,
      total: 7,
      category: "Activity",
      rarity: "Rare"
    }
  ]);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'bg-gray-100 text-gray-800 border-gray-300';
      case 'Rare': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'Epic': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'Legendary': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Friendship': return 'bg-pink-50 text-pink-700';
      case 'Adventure': return 'bg-green-50 text-green-700';
      case 'Games': return 'bg-blue-50 text-blue-700';
      case 'Activity': return 'bg-orange-50 text-orange-700';
      case 'Leadership': return 'bg-purple-50 text-purple-700';
      default: return 'bg-gray-50 text-gray-700';
    }
  };

  return (
    <div className="space-y-6">
      {/* Fun Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-purple-600 mb-2">🏆 Your Amazing Badges!</h2>
        <p className="text-lg text-gray-600">Collect badges by playing outside and making friends</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="text-center bg-purple-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">{earnedBadges.length}</div>
            <div className="text-sm text-purple-700">Badges Earned</div>
          </CardContent>
        </Card>
        <Card className="text-center bg-blue-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{availableBadges.length}</div>
            <div className="text-sm text-blue-700">In Progress</div>
          </CardContent>
        </Card>
        <Card className="text-center bg-green-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">85%</div>
            <div className="text-sm text-green-700">Weekly Goal</div>
          </CardContent>
        </Card>
        <Card className="text-center bg-yellow-50">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">🌟</div>
            <div className="text-sm text-yellow-700">Streak: 4 days</div>
          </CardContent>
        </Card>
      </div>

      {/* Earned Badges */}
      <div>
        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Star className="h-6 w-6 text-yellow-600" />
          Your Earned Badges
        </h3>
        
        <div className="grid md:grid-cols-2 gap-4">
          {earnedBadges.map((badge) => (
            <Card key={badge.id} className="hover:shadow-lg transition-all border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className="text-4xl">{badge.emoji}</div>
                    <div>
                      <CardTitle className="text-lg">{badge.name}</CardTitle>
                      <CardDescription>{badge.description}</CardDescription>
                    </div>
                  </div>
                  <Badge className={getRarityColor(badge.rarity)}>
                    {badge.rarity}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <Badge className={getCategoryColor(badge.category)} variant="outline">
                    {badge.category}
                  </Badge>
                  <div className="text-sm text-gray-600 flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {badge.dateEarned}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Badges in Progress */}
      <div>
        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <span className="text-2xl">🎯</span>
          Badges to Earn
        </h3>
        
        <div className="grid md:grid-cols-2 gap-4">
          {availableBadges.map((badge) => (
            <Card key={badge.id} className="hover:shadow-lg transition-all border-dashed border-2">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className="text-4xl opacity-60">{badge.emoji}</div>
                    <div>
                      <CardTitle className="text-lg text-gray-700">{badge.name}</CardTitle>
                      <CardDescription>{badge.description}</CardDescription>
                    </div>
                  </div>
                  <Badge className={getRarityColor(badge.rarity)}>
                    {badge.rarity}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Progress Bar */}
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{badge.progress}/{badge.total}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all"
                        style={{ width: `${(badge.progress / badge.total) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <Badge className={getCategoryColor(badge.category)} variant="outline">
                      {badge.category}
                    </Badge>
                    <div className="text-sm text-gray-600">
                      {badge.total - badge.progress} more to go!
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Encouragement Card */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="text-center py-8">
          <div className="text-4xl mb-4">🌟</div>
          <h3 className="text-xl font-semibold text-green-900 mb-2">Keep Playing & Exploring!</h3>
          <p className="text-green-700 mb-4">
            The more you play outside and make friends, the more amazing badges you'll earn!
          </p>
          <Button className="bg-green-600 hover:bg-green-700">
            🏃‍♀️ Go Play & Earn More!
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default BadgeCollection;
