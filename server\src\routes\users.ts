import express from 'express';
import bcrypt from 'bcryptjs';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { validate, updateUserSchema } from '../validation/schemas.js';
import { asyncHandler, createError } from '../middleware/errorHandler.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get current user profile
router.get('/profile', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      phone: true,
      avatar: true,
      createdAt: true,
      children: {
        select: {
          id: true,
          name: true,
          age: true,
          interests: true,
          status: true,
          avatar: true,
          currentLocation: true,
          createdAt: true
        }
      }
    }
  });

  if (!user) {
    throw createError('User not found', 404);
  }

  res.json({ user });
}));

// Update user profile
router.put('/profile', validate(updateUserSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const updateData = req.body;

  const user = await prisma.user.update({
    where: { id: req.user!.id },
    data: updateData,
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      phone: true,
      avatar: true,
      createdAt: true,
      updatedAt: true
    }
  });

  res.json({
    message: 'Profile updated successfully',
    user
  });
}));

// Change password
router.put('/password', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    throw createError('Current password and new password are required', 400);
  }

  if (newPassword.length < 6) {
    throw createError('New password must be at least 6 characters long', 400);
  }

  // Get current user with password
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id }
  });

  if (!user) {
    throw createError('User not found', 404);
  }

  // Verify current password
  const isValidPassword = await bcrypt.compare(currentPassword, user.password);
  if (!isValidPassword) {
    throw createError('Current password is incorrect', 400);
  }

  // Hash new password
  const saltRounds = 12;
  const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

  // Update password
  await prisma.user.update({
    where: { id: req.user!.id },
    data: { password: hashedNewPassword }
  });

  res.json({
    message: 'Password updated successfully'
  });
}));

// Get user statistics
router.get('/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // Get children count
  const childrenCount = await prisma.child.count({
    where: { parentId: userId }
  });

  // Get play requests statistics
  const sentRequests = await prisma.playRequest.count({
    where: { senderId: userId }
  });

  const receivedRequests = await prisma.playRequest.count({
    where: { receiverId: userId }
  });

  const approvedRequests = await prisma.playRequest.count({
    where: {
      OR: [
        { senderId: userId },
        { receiverId: userId }
      ],
      status: 'APPROVED'
    }
  });

  // Get play sessions for user's children
  const playSessions = await prisma.playSession.findMany({
    where: {
      child: {
        parentId: userId
      }
    },
    include: {
      child: {
        select: {
          id: true,
          name: true
        }
      },
      park: {
        select: {
          id: true,
          name: true
        }
      },
      game: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { startTime: 'desc' },
    take: 10
  });

  // Calculate total play time
  const totalPlayTime = playSessions.reduce((total, session) => {
    return total + (session.duration || 0);
  }, 0);

  // Get badges earned by user's children
  const earnedBadges = await prisma.userBadge.count({
    where: {
      earned: true,
      child: {
        parentId: userId
      }
    }
  });

  // Get unique parks visited
  const uniqueParks = new Set(playSessions.map(session => session.park.id)).size;

  // Get activity by month (last 6 months)
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  const monthlyActivity = await prisma.playSession.groupBy({
    by: ['startTime'],
    where: {
      child: {
        parentId: userId
      },
      startTime: {
        gte: sixMonthsAgo
      }
    },
    _count: { id: true },
    _sum: { duration: true }
  });

  // Group by month
  const activityByMonth = monthlyActivity.reduce((acc, session) => {
    const month = session.startTime.toISOString().substring(0, 7); // YYYY-MM
    if (!acc[month]) {
      acc[month] = { sessions: 0, totalTime: 0 };
    }
    acc[month].sessions += session._count.id;
    acc[month].totalTime += session._sum.duration || 0;
    return acc;
  }, {} as Record<string, { sessions: number; totalTime: number }>);

  res.json({
    overview: {
      childrenCount,
      totalPlayTime,
      earnedBadges,
      uniqueParks,
      totalSessions: playSessions.length
    },
    requests: {
      sent: sentRequests,
      received: receivedRequests,
      approved: approvedRequests
    },
    recentSessions: playSessions,
    monthlyActivity: activityByMonth
  });
}));

// Delete user account
router.delete('/account', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { password } = req.body;

  if (!password) {
    throw createError('Password confirmation required', 400);
  }

  // Get current user with password
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id }
  });

  if (!user) {
    throw createError('User not found', 404);
  }

  // Verify password
  const isValidPassword = await bcrypt.compare(password, user.password);
  if (!isValidPassword) {
    throw createError('Password is incorrect', 400);
  }

  // Delete user (cascade will handle related records)
  await prisma.user.delete({
    where: { id: req.user!.id }
  });

  res.json({
    message: 'Account deleted successfully'
  });
}));

export default router;
